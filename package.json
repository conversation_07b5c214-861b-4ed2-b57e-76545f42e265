{"name": "chat-assistant", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "tauri": "tauri"}, "devDependencies": {"@supabase/supabase-js": "^2.46.2", "@tauri-apps/api": "^2", "@tauri-apps/cli": "^2", "@tauri-apps/plugin-shell": "^2", "@types/markdown-it": "^14.1.2", "@types/uuid": "^10.0.0", "@vicons/antd": "^0.12.0", "@vicons/fa": "^0.12.0", "@vicons/fluent": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "@vicons/tabler": "^0.12.0", "@vite-pwa/assets-generator": "^0.2.6", "@vitejs/plugin-vue": "^5.0.5", "@vitest/ui": "^3.2.4", "@vue/test-utils": "^2.4.6", "@vueuse/core": "^12.0.0", "@vueuse/integrations": "^13.0.0", "axios": "^1.7.2", "jsdom": "^26.1.0", "less": "^4.2.1", "markdown-it": "^14.1.0", "naive-ui": "^2.40.2", "pinia": "^2.2.8", "typescript": "5.6.2", "vite": "^6.2.3", "vite-plugin-vue-mcp": "^0.3.2", "vitest": "^3.2.4", "vue": "^3.3.4", "vue-tsc": "2.0.29"}, "dependencies": {"@langchain/core": "^0.3.42", "@tailwindcss/vite": "^4.0.3", "@tauri-apps/plugin-http": "^2.0.1", "@tauri-apps/plugin-process": "^2.2.0", "@tauri-apps/plugin-updater": "^2.5.0", "@tdesign-vue-next/chat": "^0.1.4", "appwrite": "^16.1.0", "dexie": "^4.0.11", "idb-keyval": "^6.2.1", "langchain": "^0.3.19", "markdown-it-code-copy": "^0.2.1", "openai": "^4.77.3", "tailwindcss": "^4.0.3", "tdesign-mobile-vue": "^1.8.2", "tdesign-vue-next": "^1.10.5", "uuid": "^11.1.0", "vue-router": "^4.5.0"}}