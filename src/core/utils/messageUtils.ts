import { MessageContent } from "@/core/types/chat.ts";

/**
 * 将MessageContent转换为字符串
 * @param content 消息内容
 * @returns 转换后的字符串
 */
export function contentToString(content: MessageContent): string {
    if (typeof content === "string") {
        return content;
    } else if (Array.isArray(content)) {
        return content
            .map((item) => {
                if (typeof item === "string") {
                    return item;
                } else if (item.type === "image") {
                    return item.alt ? `[图片: ${item.alt}]` : "[图片]";
                }
                return "";
            })
            .join("\n");
    } else if (
        content &&
        typeof content === "object" &&
        content.type === "image"
    ) {
        return content.alt ? `[图片: ${content.alt}]` : "[图片]";
    }
    return "";
}

/**
 * 获取消息预览文本
 * @param content 消息内容
 * @param maxLength 最大长度
 * @returns 预览文本
 */
export function getPreviewText(
    content: MessageContent | undefined,
    maxLength: number = 30
): string {
    if (!content) return "新会话";

    if (typeof content === "string") {
        return content.slice(0, maxLength);
    } else if (Array.isArray(content)) {
        const firstItem = content[0];
        if (typeof firstItem === "string") {
            return firstItem.slice(0, maxLength);
        } else if (firstItem?.type === "image") {
            return firstItem.alt
                ? `[图片: ${firstItem.alt.slice(0, 20)}]`
                : "[图片]";
        }
    } else if (content.type === "image") {
        return content.alt ? `[图片: ${content.alt.slice(0, 20)}]` : "[图片]";
    }

    return "新会话";
}

/**
 * 处理内容中的<think>标签
 * @param content 消息内容
 * @returns 处理后的主要内容和思考内容
 */
export function processContent(content: MessageContent) {
    if (typeof content !== "string") {
        return { mainContent: contentToString(content), thinkContent: "" };
    }

    const thinkStartIndex = content.indexOf("<think>");
    if (thinkStartIndex === -1) {
        return { mainContent: content.trim(), thinkContent: "" };
    }

    const thinkEndIndex = content.indexOf("</think>", thinkStartIndex);
    const thinkStartTag = "<think>";

    if (thinkEndIndex === -1) {
        // 如果只有开始标签，将其后的所有内容作为思考内容
        const mainContent = content.substring(0, thinkStartIndex).trim();
        const thinkContent = content
            .substring(thinkStartIndex + thinkStartTag.length)
            .trim();
        return { mainContent, thinkContent };
    } else {
        // 如果有完整的标签对，保持原有逻辑
        const thinkContent = content
            .substring(thinkStartIndex + thinkStartTag.length, thinkEndIndex)
            .trim();
        const mainContent = (
            content.substring(0, thinkStartIndex) +
            content.substring(thinkEndIndex + "</think>".length)
        ).trim();
        return { mainContent, thinkContent };
    }
}

/**
 * 检测内容中是否包含思维链
 * @param content 消息内容
 * @returns 是否包含思维链开始标签
 */
export function hasThinkingContent(content: MessageContent): boolean {
    if (typeof content !== "string") {
        return false;
    }
    return content.includes("<think>");
}

/**
 * 检测思维链是否已完成
 * @param content 消息内容
 * @returns 是否包含完整的思维链标签对
 */
export function isThinkingCompleted(content: MessageContent): boolean {
    if (typeof content !== "string") {
        return false;
    }
    return content.includes("<think>") && content.includes("</think>");
}
