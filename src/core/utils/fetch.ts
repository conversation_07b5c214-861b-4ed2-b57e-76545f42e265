// 使用原生fetch而不是tauri plugin http，通过本地代理服务器处理跨域请求
/**
 * 代理 fetch 实现，通过本地代理服务器处理跨域请求
 */
const proxyFetch = (
    input: RequestInfo | URL,
    init?: RequestInit
): Promise<Response> => {
    let url = typeof input === "string" ? input : input.toString();
    let headers = new Headers(init?.headers);

    // 将原始URL保存在头信息中
    headers.set("X-Target-URL", url);
    // 修改URL指向本地代理
    url = `http://127.0.0.1:37542/proxy/`;

    // 使用原生fetch，它支持流式传输
    return window.fetch(url, {
        ...init,
        headers,
    });
};

/**
 * 根据运行环境同步选择合适的 fetch 实现。
 * 在 Tauri 环境中，使用 proxyFetch；在 Web 环境中，使用原生 fetch。
 */
const getEnvironmentFetchSync = (): typeof fetch => {
    // @ts-ignore
    if (window.__TAURI__ && typeof window.__TAURI__ === "object") {
        return proxyFetch;
    } else {
        return window.fetch.bind(window);
    }
};

/**
 * 统一导出：根据环境自动选择的 fetch 实现
 */
export const resolvedEnvFetch = getEnvironmentFetchSync();

/**
 * 兼容原 proxyFetch 导出
 */
export { proxyFetch as fetch };
