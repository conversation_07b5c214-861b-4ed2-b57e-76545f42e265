/**
 * 聊天应用的核心数据结构定义
 * 包含所有与聊天相关的接口、类型和枚举
 */

// ==================== 图片数据相关类型 ====================

/**
 * 图片数据接口 - 用于消息中的图片内容，
 */
export interface ImageData {
    type: "image";
    imageId: number; // ID from image.db
    alt?: string; // 可选的图片描述
    mimeType?: string; // 图片MIME类型，如 'image/jpeg'
    // 可选: width/height，如果能在存入 image.db 时获取并存储，对前端渲染有好处
}

/**
 * 图片记录接口 - 用于持久化存储图片
 */
export interface ImageRecord {
    id?: number; // 自动生成的主键 ID
    name: string; // 图片名称
    blob: Blob; // 图片二进制数据
    mimeType: string; // 图片 MIME 类型
    size: number; // 文件大小（字节）
    createdAt: Date; // 创建时间
    metadata?: Record<string, any>; // 可选的元数据
}

// ==================== 消息相关类型 ====================

/**
 * 消息内容 - 可以是文本、图片或文本与图片的混合数组
 */
export type MessageContent = string | ImageData | (string | ImageData)[];

/**
 * 消息角色枚举
 */
export type MessageRole = "user" | "assistant";

/**
 * 消息对象接口 - 表示一条完整的消息
 */
export interface Msg {
    content: MessageContent; // 消息内容
    short: string; // 消息简要预览
    role: MessageRole; // 消息角色：用户/助手
    id: string; // 消息唯一标识
    status: MessageStatus; // 消息流转状态
    model?: string; // 使用的AI模型
    timestamp?: number; // 消息时间戳
    thinkContent?: string; // 思考内容（仅用于显示，不进入上下文）
}

/**
 * 消息记录接口 - 用于持久化存储消息
 */
export interface MessageRecord {
    id: string; // 消息ID
    chatId: string; // 所属聊天ID
    content: MessageContent; // 消息内容
    short: string; // 消息简短描述
    role: MessageRole; // 消息角色
    model?: string; // 使用的模型
    timestamp: number; // 时间戳
    status: MessageStatus; // 消息流转状态
    thinkContent?: string; // 思考内容（仅用于显示，不进入上下文）
}

// ==================== 聊天会话相关类型 ====================

/**
 * 聊天会话接口 - 表示一个完整的聊天对话
 */
export interface Chat {
    short: string; // 会话标题
    id: string; // 会话唯一标识
    msgList: Msg[]; // 在运行时填充的消息列表，但不存储在localStorage
    time: number; // 创建时间戳
    lastMsgPreview?: string; // 最后一条消息预览
    messageCount: number; // 消息数量
}

/**
 * 聊天记录接口 - 用于持久化存储聊天会话
 */
export interface ChatRecord {
    id: string; // 聊天ID
    short: string; // 聊天标题
    time: number; // 创建时间戳
    lastMsgPreview: string; // 最后一条消息预览
    messageCount: number; // 消息数量
    updated: number; // 更新时间戳
}

// ==================== 状态管理相关类型 ====================

/**
 * 聊天存储状态接口 - 保存全局聊天状态
 */
export interface ChatStore {
    chatList: Chat[]; // 聊天会话列表
    activeChatId: string; // 当前活动会话ID
}

/**
 * 消息处理状态枚举
 */
export enum MessageStatus {
    IDLE = "idle", // 空闲
    WAITING = "waiting", // 等待响应
    THINKING = "thinking", // 思考中（输出思维链）
    GENERATING = "generating", // 生成中（输出正文）
    COMPLETED = "completed", // 已完成
    ERROR = "error", // 错误
}

/**
 * 消息发送参数接口
 */
export interface SendMessageParams {
    content: MessageContent; // 消息内容
    role?: MessageRole; // 消息角色
    model?: string; // 使用的模型
    chatId?: string; // 目标聊天ID
    short?: string; // 消息简要预览
}
