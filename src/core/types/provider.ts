import { v4 as uuidv4 } from "uuid";
import type { ProviderConfig, ProviderCreationData } from "./model.ts";

/**
 * 为供应商生成唯一ID
 * @param name 供应商名称
 * @returns 唯一ID字符串
 */
export const generateProviderId = (name: string): string =>
    `custom_${name.replace(/\s+/g, "_")}_${uuidv4()}`;

/**
 * 根据传入数据创建一个新的自定义供应商配置对象。
 * @param data 创建供应商所需的数据
 * @returns 一个完整的 ProviderConfig 对象
 */
export function createProvider(data: ProviderCreationData): ProviderConfig {
    return {
        key: generateProviderId(data.name),
        origin: "custom",
        displayName: data.name,
        apiKey: data.apiKey,
        baseUrl: data.baseUrl,
        showApiKey: true,
        showBaseUrl: true,
        showDelete: true,
        isActive: true,
        selectedModels: data.models,
        providerModels: [],
    };
}

/**
 * 合并预设供应商和用户自定义供应商列表。
 * 用户的配置会覆盖同 key 的预设配置。
 * @param presets 预设供应商列表
 * @param userConfigs 用户自定义/修改的供应商列表
 * @returns 合并后的最终供应商列表
 */
export function mergeProviders(
    presets: readonly ProviderConfig[],
    userConfigs: readonly ProviderConfig[]
): ProviderConfig[] {
    const userConfigsMap = new Map(userConfigs.map((p) => [p.key, p]));

    const mergedProviders = presets.map((preset) => {
        const userConfig = userConfigsMap.get(preset.key);
        if (userConfig) {
            userConfigsMap.delete(preset.key);
            return { ...preset, ...userConfig };
        }
        return { ...preset };
    });

    return [...mergedProviders, ...Array.from(userConfigsMap.values())];
}
