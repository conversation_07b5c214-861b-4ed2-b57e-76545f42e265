/**
 * 为了减小开发难度，我将核心数据结构维护在core文件夹中，其他ui store 必须围绕model设计
 */

/**
 * 供应商的来源类型，用于区分是应用预设还是用户自定义。
 */
export type ProviderOrigin = "preset" | "custom";

/**
 * 统一的供应商配置接口。
 * 这是应用运行时使用的最终数据结构，明确了其来源。
 */
export interface ProviderConfig {
    // --- 核心标识 ---
    key: string; // 唯一标识符
    origin: ProviderOrigin; // 数据来源：'preset' 或 'custom'

    // --- 显示信息 (来自预设或用户自定义) ---
    displayName: string;

    // --- API 配置 (预设值可能被用户覆盖) ---
    apiKey: string;
    baseUrl: string;

    // --- 功能开关 (预设值可能被用户覆盖) ---
    isActive: boolean;

    // --- UI 控制 (部分由 origin 决定) ---
    showApiKey: boolean;
    showBaseUrl: boolean;
    readonly showDelete: boolean; // 只读属性，由 origin 决定 ('custom' -> true)

    // --- 模型管理 (selectedModels 可能被用户覆盖) ---
    selectedModels: string[]; // 用户选择启用的模型

    // --- 以下是运行时状态，不应被持久化 ---
    providerModels: string[]; // 从 API 获取的全部可用模型
}

/**
 * 创建新供应商时所需的数据。
 */
export interface ProviderCreationData {
    name: string;
    baseUrl: string;
    apiKey: string;
    models: string[];
}
