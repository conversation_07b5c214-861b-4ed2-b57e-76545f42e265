// 供应商类型定义
import type { ProviderConfig } from "@/core/types/model.ts";

// 所有预设供应商配置 - 转换为数组形式
export const providerConfigs: ProviderConfig[] = [
    {
        key: "deepseek",
        origin: "preset",
        displayName: "DeepSeek",
        apiKey: "",
        baseUrl: "https://api.deepseek.com/v1",
        showApiKey: true,
        showBaseUrl: false,
        showDelete: false,
        isActive: false,
        selectedModels: ["deepseek-chat"],
        providerModels: ["deepseek-chat"],
    },
    {
        key: "openai",
        origin: "preset",
        displayName: "OpenAI",
        apiKey: "",
        baseUrl: "https://api.openai.com/v1",
        showApiKey: true,
        showBaseUrl: false,
        showDelete: false,
        isActive: false,
        selectedModels: ["gpt-4o"],
        providerModels: ["gpt-4o"],
    },
    {
        key: "ollama",
        origin: "preset",
        displayName: "Ollama",
        apiKey: "ollama",
        baseUrl: "http://127.0.0.1:11434/v1",
        showApiKey: false,
        showBaseUrl: true,
        showDelete: false,
        isActive: false,
        selectedModels: [],
        providerModels: [],
    },
];
