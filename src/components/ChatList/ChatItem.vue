<script setup lang="ts">
import { Chat } from "@/core/types/chat.ts";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();

const props = defineProps<{
    item: Chat;
    isActive: boolean;
}>();

const emit = defineEmits<{
    click: [item: Chat];
    contextmenu: [e: MouseEvent, id: string];
    delete: [item: Chat];
}>();

function handleClick() {
    // 检查当前路由是否为会话路由
    if (route.path !== "/") {
        // 如果不是会话路由，则导航回会话路由
        router.push("/");
    }

    emit("click", props.item);
}

function handleContextMenu(e: MouseEvent) {
    emit("contextmenu", e, props.item.id + "");
}
</script>

<template>
    <div
        class="chat-item"
        :class="{ active: isActive }"
        @click="handleClick"
        @contextmenu="handleContextMenu"
    >
        <p v-if="item">
            {{ item.short || "新的对话" }}
        </p>
    </div>
</template>

<style scoped lang="less">
.ellipsis-line(@lines: 1) {
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: @lines;
    -webkit-box-orient: vertical;
    word-break: break-all;
    word-wrap: break-word;
}

.chat-item {
    margin: 0 12px 16px 12px;
    padding: 8px;
    border-radius: 12px;
    cursor: pointer;

    &:hover,
    &.active:hover {
        background-color: var(--side-hover-bg-color);
    }

    p {
        .ellipsis-line(2);
        color: var(--side-text-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    &.active {
        background-color: var(--side-active-bg-color);

        p {
            color: var(--side-text-color);
        }
    }
}
</style>
