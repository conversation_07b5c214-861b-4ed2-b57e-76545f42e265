<template>
    <n-modal
        v-model:show="showModal"
        preset="card"
        style="width: 400px"
        title="重命名会话"
        :mask-closable="false"
    >
        <n-input
            v-model:value="inputValue"
            placeholder="请输入会话标题"
            autofocus
            :select-on-focus="true"
            size="medium"
            round
            @keydown="handleKeydown"
        />
        <template #footer>
            <div class="flex justify-end space-x-2">
                <n-button @click="handleCancel">取消</n-button>
                <n-button type="primary" @click="handleConfirm">
                    确定
                </n-button>
            </div>
        </template>
    </n-modal>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { NModal, NInput, NButton, useMessage } from "naive-ui";
import type { Chat } from "@/core/types/chat.ts";

interface Props {
    show: boolean;
    chatItem: Chat | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
    (e: "update:show", value: boolean): void;
    (e: "confirm", chatId: string, newTitle: string): void;
}>();

const message = useMessage();
const showModal = ref(false);
const inputValue = ref("");

// 监听 props.show 的变化
watch(
    () => props.show,
    (newVal) => {
        showModal.value = newVal;
        if (newVal && props.chatItem) {
            inputValue.value = props.chatItem.short || "新的对话";
        }
    },
    { immediate: true }
);

// 监听 showModal 的变化，同步到父组件
watch(showModal, (newVal) => {
    if (newVal !== props.show) {
        emit("update:show", newVal);
    }
});

const handleKeydown = (e: KeyboardEvent) => {
    if (e.key === "Enter") {
        e.preventDefault();
        handleConfirm();
    }
};

const handleCancel = () => {
    showModal.value = false;
};

const handleConfirm = () => {
    const newTitle = inputValue.value?.trim();
    
    if (!newTitle) {
        message.warning("会话标题不能为空");
        return;
    }
    
    if (!props.chatItem) {
        message.error("会话信息不存在");
        return;
    }
    
    if (newTitle === props.chatItem.short) {
        showModal.value = false;
        return;
    }
    
    emit("confirm", props.chatItem.id, newTitle);
    showModal.value = false;
};
</script>

<style scoped>
.flex {
    display: flex;
}

.justify-end {
    justify-content: flex-end;
}

.space-x-2 > * + * {
    margin-left: 8px;
}
</style>
