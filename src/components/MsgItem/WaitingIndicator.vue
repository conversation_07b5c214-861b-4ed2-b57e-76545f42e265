<template>
    <div class="waiting-indicator">
        <div class="waiting-content">
            <div class="star-container">
                <div class="star-icon rotating">✦</div>
            </div>
            <span class="waiting-text">模型响应中...</span>
        </div>
    </div>
</template>

<script setup lang="ts">
// 这个组件用于显示AI模型响应前的等待状态
</script>

<style scoped>
.waiting-indicator {
    display: flex;
    align-items: center;
    padding: 16px 0;
    min-height: 48px;
}

.waiting-content {
    display: flex;
    align-items: center;
    gap: 10px;
    opacity: 0.8;
}

.star-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
}

.star-icon {
    font-size: 18px;
    color: var(--primary-color, #18a058);
    display: inline-block;
    line-height: 1;
}

.rotating {
    animation: rotate 2s linear infinite;
    transform-origin: center;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.waiting-text {
    font-size: 14px;
    color: var(--text-color-2, #666);
    font-weight: 400;
    letter-spacing: 0.5px;
}

/* 使用 naive-ui 的 CSS 变量 */
.waiting-indicator {
    color: var(--n-text-color);
}

.waiting-text {
    color: var(--n-text-color-2);
}

.star-icon {
    color: var(--n-color-primary);
}

/* 深色模式下的额外优化 */
html.dark .waiting-content {
    opacity: 0.9;
}

html.dark .star-icon {
    filter: brightness(1.1);
}
</style>
