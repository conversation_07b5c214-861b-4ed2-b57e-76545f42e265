<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';

const props = defineProps<{
    content: string;
    isActive: boolean; // 是否正在思考中
}>();

const displayText = ref('');
const currentIndex = ref(0);
const animationTimer = ref<number | null>(null);
const lastContentLength = ref(0);

// 智能判断是否应该显示光标
const shouldShowCursor = computed(() => {
    if (!props.isActive) return false;

    // 如果内容包含完整的思考标签，不显示光标
    const hasCompleteThinkTag = props.content.includes('</think>');
    if (hasCompleteThinkTag) {
        return false;
    }

    // 如果动画正在播放且还有未显示的内容，显示光标
    const hasMoreContent = currentIndex.value < props.content.length;
    const isAnimating = animationTimer.value !== null;

    return hasMoreContent || isAnimating;
});

// 计算当前应该显示的文本
const formattedText = computed(() => {
    if (!props.isActive) {
        return props.content;
    }
    return displayText.value;
});

// 启动增量打字机动画 - 与网络chunk同步
const startIncrementalAnimation = (fromIndex: number = 0) => {
    if (!props.isActive || !props.content) return;

    // 直接显示到当前内容长度，与网络返回同步
    displayText.value = props.content;
    currentIndex.value = props.content.length;

    console.log('[ThinkingAnimation] Synced display to network chunk:', {
        contentLength: props.content.length,
        displayLength: displayText.value.length
    });
};

// 停止动画
const stopAnimation = () => {
    if (animationTimer.value) {
        clearTimeout(animationTimer.value);
        animationTimer.value = null;
    }
};

// 监听内容变化 - 与网络chunk同步显示
watch(() => props.content, (newContent, oldContent) => {
    console.log('[ThinkingAnimation] Content changed:', {
        isActive: props.isActive,
        oldLength: oldContent?.length || 0,
        newLength: newContent?.length || 0,
        hasCompleteThinkTag: newContent?.includes('</think>') || false
    });

    if (props.isActive && newContent) {
        // 直接显示新内容，与网络返回同步
        displayText.value = newContent;
        currentIndex.value = newContent.length;

        // 检测是否包含完整的思考结束标签
        const hasCompleteThinkTag = newContent.includes('</think>');
        if (hasCompleteThinkTag) {
            console.log('[ThinkingAnimation] Think tag completed, content fully displayed');
        }
    } else if (!props.isActive) {
        displayText.value = newContent;
    }

    lastContentLength.value = newContent?.length || 0;
}, { immediate: true });

// 监听激活状态变化
watch(() => props.isActive, (isActive) => {
    console.log('[ThinkingAnimation] Active state changed:', isActive);
    if (isActive) {
        // 立即显示当前内容
        displayText.value = props.content;
        currentIndex.value = props.content.length;
    } else {
        stopAnimation();
        displayText.value = props.content;
    }
});

onMounted(() => {
    if (props.isActive) {
        // 立即显示当前内容
        displayText.value = props.content;
        currentIndex.value = props.content.length;
    }
});

onUnmounted(() => {
    stopAnimation();
});
</script>

<template>
    <div class="thinking-animation">
        <div class="text-container">
            <div 
                class="animated-text"
                :class="{ 'typing': isActive }"
                v-html="formattedText"
            />
            <div
                v-if="shouldShowCursor"
                class="cursor"
            />
        </div>
    </div>
</template>

<style scoped lang="less">
.thinking-animation {
    .text-container {
        position: relative;
        display: inline-block;
        min-height: 1.2em;
        line-height: 1.5;
    }
    
    .animated-text {
        display: inline-block;
        word-wrap: break-word;
        word-break: break-word;
        white-space: pre-wrap;
        
        &.typing {
            animation: textFlow 0.3s ease-in-out;
        }
    }
    
    .cursor {
        display: inline-block;
        width: 2px;
        height: 1.2em;
        background-color: var(--primary-color, #18a058);
        margin-left: 2px;
        animation: blink 1s infinite;
        vertical-align: text-bottom;
    }
}

@keyframes textFlow {
    0% {
        opacity: 0.7;
        transform: translateY(2px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}
</style>
