<script setup lang="ts">
import { Refresh } from "@vicons/ionicons5";
import { ChevronDown, ChevronUp } from "@vicons/ionicons5";
import { Copy } from "@vicons/tabler";
import { useThemeVars, NSkeleton } from "naive-ui";
import { Msg, ImageData as MsgImageData, MessageStatus } from "@/core/types/chat.ts"; // Renamed ImageData to MsgImageData to avoid conflict
import { useEnvStore } from "@/store/useEnvStore.ts";
import { ref, computed, watch } from "vue";
import { useMessage } from "naive-ui";
import { contentToString, processContent } from "@/core/utils/messageUtils.ts";
import ImageDisplayComponent from "@/components/MsgItem/ImageDisplayComponent.vue"; // Import the new component
import MsgContent from "@/components/MsgItem/MsgContent.vue"; // Import the MsgContent component
import ThinkingAnimation from "@/components/MsgItem/ThinkingAnimation.vue"; // Import the ThinkingAnimation component
import WaitingIndicator from "@/components/MsgItem/WaitingIndicator.vue"; // Import the WaitingIndicator component
import { useLinkInterceptor } from "@/components/MsgItem/useLinkInterceptor.ts";
import { useMarkdown } from "@/components/MsgItem/useMarkdown.ts";

const message = useMessage();

const props = defineProps<{
    item: Msg;
}>();

const emit = defineEmits<{
    retry: [item: Msg];
}>();

const envStore = useEnvStore();
const themeVars = useThemeVars();
const { md } = useMarkdown(); // 使用共享的 markdown 实例
const isThinkExpanded = ref(false); // 默认不展开思考过程

// 判断思考内容是否应该显示
const shouldShowThinkContent = computed(() => {
    const hasThinkContent = !!props.item.thinkContent;
    const isFlowing = isThinkContentFlowing.value;
    const isExpanded = isThinkExpanded.value;
    const result = hasThinkContent && (isFlowing || isExpanded);
    console.log('[MsgItem] shouldShowThinkContent computed:', {
        messageId: props.item.id,
        hasThinkContent,
        isFlowing,
        isExpanded,
        result
    });
    return result;
});

// 判断是否应该显示等待指示器
const shouldShowWaitingIndicator = computed(() => {
    // 只有当消息是assistant且正在等待首字符时才显示等待指示器
    // 使用status状态来判断是否正在等待首字符
    const result = props.item.role === "assistant" && props.item.status === MessageStatus.WAITING;
    console.log('[MsgItem] shouldShowWaitingIndicator computed:', {
        messageId: props.item.id,
        role: props.item.role,
        status: props.item.status,
        result
    });
    return result;
});

// 判断思考内容是否正在流式传输
const isThinkContentFlowing = computed(() => {
    const hasThinkContent = !!props.item.thinkContent;
    const isFlowing = props.item.status === MessageStatus.THINKING ||
                      props.item.status === MessageStatus.GENERATING ||
                      props.item.status === MessageStatus.WAITING;
    const result = hasThinkContent && isFlowing;
    console.log('[MsgItem] isThinkContentFlowing computed:', {
        messageId: props.item.id,
        status: props.item.status,
        hasThinkContent,
        isFlowing,
        result
    });
    return result;
});

// 判断是否处于纯粹的“思考”状态（用于显示思考动画图标）
const isActivelyThinking = computed(() => {
    const result = props.item.status === MessageStatus.THINKING;
    console.log('[MsgItem] isActivelyThinking computed:', {
        messageId: props.item.id,
        status: props.item.status,
        result
    });
    return result;
});

// 思考过程的标题文本
const thinkTitle = computed(() => {
    if (isThinkContentFlowing.value) {
        return "模型正在思考中";
    }
    return "思考过程";
});

function handleRetry() {
    emit("retry", props.item);
}

function toggleThink() {
    isThinkExpanded.value = !isThinkExpanded.value;
}

function handleCopy() {
    const contentStr = contentToString(props.item.content);
    navigator.clipboard.writeText(contentStr).then(() => {
        message.success("内容已复制到剪贴板");
    });
}

// 思考过程完成后，自动折叠
watch(() => props.item.status, (newStatus: MessageStatus, oldStatus: MessageStatus) => {
    console.log('[MsgItem] Status changed:', {
        messageId: props.item.id,
        oldStatus,
        newStatus,
        hasThinkContent: !!props.item.thinkContent,
        isExpanded: isThinkExpanded.value
    });

    // 当思考完成（从THINKING转为GENERATING）时，自动折叠思考过程
    if (
        oldStatus === MessageStatus.THINKING &&
        newStatus === MessageStatus.GENERATING &&
        props.item.thinkContent
    ) {
        console.log('[MsgItem] Thinking completed, auto-collapsing think content');
        isThinkExpanded.value = false;
    }

    // 当消息完成时，确保思考过程是折叠的
    if (newStatus === MessageStatus.COMPLETED && props.item.thinkContent) {
        console.log('[MsgItem] Message completed, ensuring think content is collapsed');
        isThinkExpanded.value = false;
    }
});

// 当思考内容第一次出现时，自动展开
watch(() => props.item.thinkContent, (newContent, oldContent) => {
    console.log('[MsgItem] Think content changed:', {
        messageId: props.item.id,
        hadContent: !!oldContent,
        hasContent: !!newContent,
        status: props.item.status,
        isExpanded: isThinkExpanded.value
    });

    if (newContent && !oldContent && props.item.status === MessageStatus.THINKING) {
        console.log('[MsgItem] First think content appeared, auto-expanding');
        isThinkExpanded.value = true;
    }
});

// 监听展开状态变化
watch(() => isThinkExpanded.value, (newValue, oldValue) => {
    console.log('[MsgItem] Expand state changed:', {
        messageId: props.item.id,
        oldValue,
        newValue,
        status: props.item.status
    });
});

// 设置链接拦截
const bubbleRef = ref<HTMLElement | null>(null);
useLinkInterceptor(
    bubbleRef,
    ".message-content-area",
    () => props.item.content
);
</script>

<template>
    <div
        ref="bubbleRef"
        class="gap-5 bubble max-w-full"
        :class="
            item.role === 'user'
                ? 'user pt-2 pb-2 pr-4 pl-4 bg-[#e8e8e880] dark:bg-[#323232d9] '
                : 'assistant w-full'
        "
        :style="{
            color: themeVars.textColor1,
        }"
    >
        <n-flex vertical class="content grow min-w-0">
            <div
                v-if="item.thinkContent"
                class="think-block"
            >
                <div class="think-header" @click="toggleThink">
                    <div class="think-title">{{ thinkTitle }}</div>
                    <n-icon
                        v-if="!isActivelyThinking"
                        size="16"
                        class="toggle-icon"
                    >
                        <component
                            :is="isThinkExpanded ? ChevronUp : ChevronDown"
                        />
                    </n-icon>
                    <div
                        v-else
                        class="thinking-icon"
                    >
                        <div class="star-icon">✦</div>
                    </div>
                </div>
                <div v-show="shouldShowThinkContent">
                    <ThinkingAnimation
                        v-if="isThinkContentFlowing"
                        :content="item.thinkContent || ''"
                        :is-active="true"
                    />
                    <div
                        v-else
                        v-html="md.render(item.thinkContent)"
                    />
                </div>
            </div>
            <div v-if="shouldShowWaitingIndicator">
                <WaitingIndicator />
            </div>
            <!-- New rendering logic for MessageContent -->
            <MsgContent v-else :content="item.content" />
            <div
                v-if="item.role === 'assistant' && item.status === MessageStatus.COMPLETED"
                class="flex justify-between items-center"
            >
                <div class="flex gap-2">
                    <n-button text size="tiny" @click="handleRetry">
                        <n-icon>
                            <Refresh />
                        </n-icon>
                        重新生成
                    </n-button>
                    <n-button text size="tiny" @click="handleCopy">
                        <n-icon>
                            <Copy />
                        </n-icon>
                        复制
                    </n-button>
                </div>
                <div v-if="item.model" class="model-info">
                    {{ item.model }}
                </div>
            </div>
        </n-flex>
    </div>
</template>

<style scoped lang="less">
.bubble {
    margin-bottom: 24px;
    display: flex;
    align-items: flex-start;
}

.user {
    width: fit-content;
    border-radius: 12px;
    align-self: flex-end;
}

.assistant {
    border-radius: 12px;
    padding: 0;
}

.think-block {
    margin-top: 12px;
    padding: 12px 16px;
    /* 创建明显但协调的背景色 */
    background-color: color-mix(in srgb, var(--body-color) 85%, var(--primary-color) 8%);
    border-radius: 8px;
    border: 1px solid color-mix(in srgb, var(--border-color) 70%, var(--primary-color) 15%);
    transition: all 0.3s ease;
    position: relative;

    /* 添加微妙的内阴影增强层次感 */
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);

    &:hover {
        background-color: color-mix(in srgb, var(--body-color) 80%, var(--primary-color) 12%);
        border-color: color-mix(in srgb, var(--border-color) 60%, var(--primary-color) 20%);
    }

    .think-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        margin-bottom: 8px;

        &:hover {
            opacity: 0.8;
        }
    }

    .think-title {
        font-weight: 500;
        color: var(--text-color-2);
        font-size: 13px;
    }

    .toggle-icon {
        color: var(--text-color-3);
        transition: all 0.2s ease;
        cursor: pointer;

        &:hover {
            color: var(--text-color-2);
        }
    }

    .thinking-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-color);

        .star-icon {
            font-size: 14px;
            animation: rotate 2s linear infinite;
            opacity: 0.8;
        }
    }
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.model-info {
    font-size: 12px;
    color: #999;
    padding: 4px 8px;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 4px;
}
</style>
