<script setup lang="ts">
import { lightTheme, darkTheme, GlobalThemeOverrides } from "naive-ui";
import { onMounted, ref, watchEffect, computed, onUnmounted, watch } from "vue";
import { useEnvStore } from "./store/useEnvStore";
import { usePreferredColorScheme } from "@vueuse/core";
import { checkAndDownloadUpdate } from "@/core/utils/updater";
import { useModelStore } from "@/store/useModelStore.ts";
import { useGuideStore } from "@/store/useGuideStore.ts";
import UserGuideModal from "@/components/common/UserGuideModal.vue";
import { useRoute } from "vue-router";

const theme = ref(darkTheme);
const envStore = useEnvStore();
const preferredColor = usePreferredColorScheme();

const commonThemeOverrides = {
    common: {
        borderRadius: "16px",
    },
    Input: {
        boxShadowFocus:
            "0 9px 9px 0px rgba(0,0,0,.01),0 2px 5px 0px rgba(0,0,0,.06)",
        borderHover: "1px solid rgba(0,0,0,.1)",
        borderFocus: "1px solid rgba(0,0,0,.1)",
        border: "1px solid rgba(0,0,0,.1)",
        colorFocus: "rgba(255,255,255,.1)",
        fontSizeLarge: "16px",
    },
    Popover: {
        borderRadius: "9px",
        color: "#2f2f2f",
        padding: "8px",
    },
};

const darkThemeOverrides: GlobalThemeOverrides = {
    ...commonThemeOverrides,
    common: {
        ...commonThemeOverrides.common,
        primaryColor: "#fff",
        primaryColorHover: "rgb(255,255,255)",
        primaryColorPressed: "rgb(200,200,200)",
    },
};

const lightThemeOverrides: GlobalThemeOverrides = {
    ...commonThemeOverrides,
    common: {
        ...commonThemeOverrides.common,
        primaryColor: "#000",
        primaryColorHover: "rgb(0,0,0)",
        primaryColorPressed: "rgb(100,100,100)",
    },
    Popover: {
        ...commonThemeOverrides.Popover,
        color: "#fff",
    },
};

const themeOverrides = computed(() =>
    preferredColor.value === "dark" ? darkThemeOverrides : lightThemeOverrides
);

const modelStore = useModelStore();
const guideStore = useGuideStore();
const route = useRoute();

// 检查是否需要显示新手引导的函数
const checkAndShowGuide = () => {
    // 只在聊天页面（主页）显示新手引导，设置页面不显示
    const isChatPage = route.path === "/";

    // 如果没有模型配置且在聊天页面且当前未显示引导且未选择“不再提醒”，显示新手引导
    if (
        modelStore.allModelList.length === 0 &&
        isChatPage &&
        !guideStore.showGuide &&
        !guideStore.neverShowGuide
    ) {
        guideStore.openGuide();
    }
};

onMounted(async () => {
    // 只在桌面平台检查并静默下载更新
    if (envStore.isDesktop) {
        checkAndDownloadUpdate();
    }

    // 初始化模型服务
    await modelStore.initialize();
});

// 监听路由变化，切换路由后也检查是否需要显示新手引导
watch(
    () => route.path,
    () => {
        checkAndShowGuide();
    }
);

watchEffect(() => {
    const isDark = preferredColor.value === "dark";
    envStore.themeStatus = isDark ? "dark" : "light";
    theme.value = isDark ? darkTheme : lightTheme;
});
</script>

<template>
    <n-message-provider>
        <n-dialog-provider>
            <n-config-provider :theme="theme" :theme-overrides="themeOverrides">
                <n-notification-provider placement="bottom-right">
                    <UserGuideModal
                        :show="guideStore.showGuide"
                        @close="guideStore.closeGuide"
                        @never-show="guideStore.setNeverShowGuide"
                    />
                    <div class="w-[100dvw] h-[100dvh] flex">
                        <router-view
                            :class="{
                                light: envStore.themeStatus === 'light',
                                dark: envStore.themeStatus === 'dark',
                            }"
                        />
                    </div>
                </n-notification-provider>
            </n-config-provider>
        </n-dialog-provider>
    </n-message-provider>
</template>
