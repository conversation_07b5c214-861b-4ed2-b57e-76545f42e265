<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { useModelStore } from "@/store/useModelStore.ts";
import { NButton, NIcon, useMessage, NInput } from "naive-ui";
import { DeleteFilled, ReloadOutlined } from "@vicons/antd";
import { Add } from "@vicons/ionicons5";
import type { ProviderConfig } from "@/core/types/model.ts";
import AddCustomModelModal from "./AddCustomModelModal.vue";

interface Props {
    selectedProvider: ProviderConfig;
}

const props = defineProps<Props>();
const modelStore = useModelStore();
const message = useMessage();

const providerModelManagement = reactive({
    isLoading: false,
});

const showAllModels = ref(false);

const filterKeyword = ref("");

const getCurrentDisplayModels = computed(() => {
    const providerId = props.selectedProvider.key;
    const models = showAllModels.value
        ? modelStore.getProviderModels(providerId)
        : modelStore.getProviderSelectedModels(providerId);

    if (!filterKeyword.value) {
        return models;
    }
    return models.filter((model: string) =>
        model.toLowerCase().includes(filterKeyword.value.toLowerCase())
    );
});

const fetchAllModels = async () => {
    const providerId = props.selectedProvider.key;
    const provider = modelStore.getProvider(providerId);

    if (!provider?.baseUrl || !provider?.apiKey) {
        message.warning("请先配置 Base URL 和 API Key");
        return;
    }

    providerModelManagement.isLoading = true;

    try {
        await modelStore.fetchProviderModels(providerId);
        // message.success("全部模型获取成功");
        showAllModels.value = true;
    } catch (error) {
        message.warning(String(error));
    } finally {
        providerModelManagement.isLoading = false;
    }
};

const switchToSelectedModels = () => {
    showAllModels.value = false;
};

const addAllModelsToSelected = () => {
    const providerId = props.selectedProvider.key;
    const availableModels = modelStore.getProviderModels(providerId);
    const unselectedModels = availableModels.filter(
        (model: string) => !isModelSelected(model)
    );

    if (unselectedModels.length === 0) {
        message.info("所有可用模型都已添加");
        // 即使没有新模型添加，也返回已选择列表
        showAllModels.value = false;
        return;
    }

    unselectedModels.forEach((modelName: string) => {
        modelStore.addModelToProvider(providerId, modelName);
    });
    message.success(`已成功添加 ${unselectedModels.length} 个模型`);

    // 添加完成后自动返回已选择列表
    showAllModels.value = false;
};

const addModelToSelected = (modelName: string) => {
    modelStore.addModelToProvider(props.selectedProvider.key, modelName);
    message.success(`模型 ${modelName} 已添加到已选择列表`);
};

const removeModelFromSelected = (modelName: string) => {
    modelStore.removeModelFromProvider(props.selectedProvider.key, modelName);
    message.success(`模型 ${modelName} 已从已选择列表移除`);
};

const isModelSelected = (modelName: string) => {
    return modelStore.isModelSelectedByProvider(
        props.selectedProvider.key,
        modelName
    );
};

const showAddCustomModelModal = ref(false);

const handleAddCustomModel = (modelName: string) => {
    const providerId = props.selectedProvider.key;
    modelStore.addModelToProvider(providerId, modelName);
    message.success(`自定义模型 ${modelName} 添加成功`);
};
</script>

<template>
    <div class="config-group">
        <div class="flex items-center justify-between mb-3">
            <label class="text-sm font-medium">
                {{ showAllModels ? "全部模型" : "已选择模型" }}
            </label>
            <div class="flex gap-2 space-x-2">
                <n-button
                    v-if="showAllModels"
                    size="small"
                    @click="switchToSelectedModels"
                >
                    返回已选择
                </n-button>
                <n-button
                    v-if="showAllModels"
                    size="small"
                    type="primary"
                    @click="addAllModelsToSelected"
                >
                    一键添加全部
                </n-button>
                <n-button
                    v-else
                    size="small"
                    @click="fetchAllModels"
                    :loading="providerModelManagement.isLoading"
                    :disabled="
                        !modelStore.getProvider(props.selectedProvider.key)
                            ?.baseUrl ||
                        !modelStore.getProvider(props.selectedProvider.key)
                            ?.apiKey
                    "
                >
                    <template #icon>
                        <n-icon><ReloadOutlined /></n-icon>
                    </template>
                    获取全部模型
                </n-button>
            </div>
        </div>

        <!-- 筛选区 -->
        <div class="mb-3">
            <n-input
                v-model:value="filterKeyword"
                placeholder="筛选模型"
                clearable
            />
        </div>

        <!-- 可用模型列表 -->
        <div
            v-if="getCurrentDisplayModels.length > 0"
            class="ios-model-list rounded-xl p-4 overflow-y-auto"
        >
            <div class="space-y-2">
                <div
                    v-for="(model, index) in getCurrentDisplayModels"
                    :key="index"
                    class="model-item flex items-center justify-between p-3 rounded-lg shadow-sm"
                >
                    <span class="text-sm">
                        {{ model }}
                    </span>
                    <div class="flex space-x-2">
                        <!-- 在全部模型模式下显示添加按钮（未选择的模型） -->
                        <n-button
                            v-if="showAllModels && !isModelSelected(model)"
                            size="tiny"
                            type="primary"
                            @click="addModelToSelected(model)"
                        >
                            <template #icon>
                                <n-icon><Add /></n-icon>
                            </template>
                            添加
                        </n-button>
                        <p v-if="showAllModels && isModelSelected(model)">
                            已添加
                        </p>
                        <!-- 移除按钮 -->
                        <n-button
                            v-if="!showAllModels"
                            size="tiny"
                            type="error"
                            text
                            @click="removeModelFromSelected(model)"
                        >
                            <template #icon>
                                <n-icon><DeleteFilled /></n-icon>
                            </template>
                            移除
                        </n-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 无模型时的提示 -->
        <div v-else class="empty-models text-center py-8">
            <p class="text-sm">
                {{ showAllModels ? "暂无全部模型" : "暂无已选择模型" }}
            </p>
            <p class="text-xs mt-1">
                {{
                    showAllModels
                        ? "请先配置 Base URL 和 API Key，然后重新获取全部模型"
                        : "请先获取全部模型，然后添加需要的模型到已选择列表"
                }}
            </p>
        </div>

        <!-- 添加自定义模型按钮 -->
        <div v-if="!showAllModels" class="mt-4 text-right">
            <n-button
                size="small"
                type="primary"
                ghost
                @click="showAddCustomModelModal = true"
            >
                <template #icon>
                    <n-icon><Add /></n-icon>
                </template>
                添加自定义模型
            </n-button>
        </div>
    </div>

    <!-- 添加自定义模型模态框 -->
    <AddCustomModelModal
        v-model:show="showAddCustomModelModal"
        @add-model="handleAddCustomModel"
    />
</template>
<style scoped>
.ios-model-list {
    background-color: var(--color-background-mute);
}
.model-item {
    background-color: var(--color-background);
}
label,
span,
p {
    color: var(--color-text);
}
.empty-models p {
    color: var(--text-secondary-color);
}
</style>


