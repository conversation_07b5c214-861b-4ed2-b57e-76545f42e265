<template>
    <div class="content-section">
        <p class="section-title">界面设置</p>
        
        <!-- AI 总结会话开关 -->
        <div class="flex items-center justify-between mb-4">
            <span class="setting-label">AI 总结会话</span>
            <n-switch v-model:value="chatStore.enableAiSummary"/>
        </div>
        <p class="setting-description mb-6">
            开启后，新建的会话将由 AI 进行总结生成标题。关闭则直接使用消息内容作为标题。
        </p>

        <!-- 指定单独的总结模型开关 -->
        <div class="flex items-center justify-between mb-4">
            <span class="setting-label">指定单独的总结模型</span>
            <n-switch v-model:value="chatStore.enableSeparateSummaryModel"/>
        </div>
        <p class="setting-description mb-4">
            开启后，可以为会话总结功能指定专门的模型，关闭时使用当前对话模型进行总结。
        </p>

        <!-- 总结模型选择器 -->
        <div v-if="chatStore.enableSeparateSummaryModel" class="ml-4 border-l-2 border-gray-200 dark:border-gray-600 pl-4">
            <div class="flex flex-col gap-3">
                <div class="flex flex-col gap-2">
                    <span class="setting-label text-sm">总结模型：</span>
                    <n-select
                        v-model:value="selectedSummaryModelKey"
                        :options="summaryModelOptions"
                        placeholder="请选择总结模型"
                        size="small"
                        class="w-full"
                        :disabled="!hasAvailableModels"
                    />
                    <span v-if="!hasAvailableModels" class="setting-hint text-red-500">
                        请先在API设置中配置并激活至少一个模型供应商
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, watch } from "vue";
import { NSwitch, NSelect } from "naive-ui";
import { useChatStore } from "@/store/useChatStore.ts";
import { useModelStore } from "@/store/useModelStore.ts";

const chatStore = useChatStore();
const modelStore = useModelStore();

// 检查是否有可用的模型
const hasAvailableModels = computed(() => {
    return modelStore.allModelList.length > 0;
});

// 构建总结模型选项列表
const summaryModelOptions = computed(() => {
    const options: Array<{ label: string; value: string }> = [];

    // 遍历所有激活的供应商
    modelStore.activeProviders.forEach(provider => {
        const selectedModels = provider.selectedModels || [];
        selectedModels.forEach(model => {
            options.push({
                label: `${model} (${provider.displayName || provider.key})`,
                value: `${provider.key}/${model}`
            });
        });
    });

    return options;
});

// 当前选中的总结模型（组合键：provider/model）
const selectedSummaryModelKey = computed({
    get: () => {
        if (chatStore.summaryProvider && chatStore.summaryModel) {
            return `${chatStore.summaryProvider}/${chatStore.summaryModel}`;
        }
        return "";
    },
    set: (value: string) => {
        if (value) {
            // 使用 indexOf 和 substring 来正确分割，避免模型名称中包含 / 的问题
            const separatorIndex = value.indexOf('/');
            if (separatorIndex !== -1) {
                const provider = value.substring(0, separatorIndex);
                const model = value.substring(separatorIndex + 1);
                chatStore.summaryProvider = provider;
                chatStore.summaryModel = model;
            } else {
                // 兼容旧格式 provider:model，进行数据迁移
                const colonIndex = value.indexOf(':');
                if (colonIndex !== -1) {
                    const provider = value.substring(0, colonIndex);
                    const model = value.substring(colonIndex + 1);
                    chatStore.summaryProvider = provider;
                    chatStore.summaryModel = model;
                }
            }
        } else {
            chatStore.summaryProvider = "";
            chatStore.summaryModel = "";
        }
    }
});

// 监听enableSeparateSummaryModel的变化，当关闭时清空选择
watch(() => chatStore.enableSeparateSummaryModel, (newValue) => {
    if (!newValue) {
        chatStore.summaryProvider = "";
        chatStore.summaryModel = "";
    }
});

// 监听可用模型列表变化，如果当前选择的模型不再可用，则清空选择
watch(() => summaryModelOptions.value, (newOptions) => {
    const currentKey = selectedSummaryModelKey.value;
    if (currentKey && !newOptions.some(option => option.value === currentKey)) {
        chatStore.summaryProvider = "";
        chatStore.summaryModel = "";
    }
});
</script>

<style scoped>
/* 设置标签 */
.setting-label {
    display: block;
    color: var(--color-text);
    font-size: 0.875rem;
}

/* 提示文字 */
.setting-hint {
    font-size: 0.75rem;
    color: var(--text-secondary-color);
}

/* 描述文字 */
.setting-description {
    font-size: 0.75rem;
    color: var(--text-secondary-color);
    line-height: 1.5;
}

/* 标题样式 */
.section-title {
    font-weight: bold;
    color: var(--color-text);
    margin-bottom: 0.5rem;
}

.content-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1.5rem;
    height: 100%;
    overflow: auto;
}
</style>
