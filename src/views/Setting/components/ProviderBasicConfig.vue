<script setup lang="ts">
import { computed } from "vue";
import { useModelStore } from "@/store/useModelStore.ts";
import { NInput, useMessage } from "naive-ui";
import type { ProviderConfig } from "@/core/types/model.ts";
import { open } from "@tauri-apps/plugin-shell";

interface Props {
    selectedProvider: ProviderConfig;
}

const props = defineProps<Props>();
const modelStore = useModelStore();
const message = useMessage();

const fullUrl = computed(() => {
    const baseUrl = props.selectedProvider.baseUrl;
    if (!baseUrl) {
        return "";
    }
    // 确保URL末尾没有斜杠，然后我们自己添加
    const trimmedUrl = baseUrl.endsWith("/") ? baseUrl.slice(0, -1) : baseUrl;
    return `${trimmedUrl}/chat/completion`;
});

const setBaseUrl = (value: string) => {
    modelStore.updateProvider(props.selectedProvider.key, { baseUrl: value });
};

const setApiKey = (value: string) => {
    modelStore.updateProvider(props.selectedProvider.key, { apiKey: value });
};

const isValidUrl = (url: string) => {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
};

const checkUrlValidity = (url: string) => {
    if (url) {
        if (!isValidUrl(url)) {
            message.warning(
                "请输入有效的URL地址（例如https://api.openai.com/v1）"
            );
        }
    }
};

function openApiKeyUrl() {
    open("https://pigai.guohere.com/get-api-key.html");
}
</script>

<template>
    <div class="space-y-6">
        <!-- Base URL 配置 -->
        <div v-if="props.selectedProvider.showBaseUrl" class="config-group">
            <label
                class="block text-sm font-medium mb-2"
            >
                Base URL
            </label>
            <n-input
                :value="props.selectedProvider.baseUrl"
                @update:value="setBaseUrl"
                :placeholder="`例如：https://api.openai.com/v1`"
                @blur="checkUrlValidity(props.selectedProvider.baseUrl || '')"
                size="large"
                class="ios-input"
            />
            <p
                v-if="props.selectedProvider.baseUrl"
                class="mt-2 text-xs"
            >
                Chat API 端点: {{ fullUrl }}
            </p>
        </div>

        <!-- API Key 配置 -->
        <div v-if="props.selectedProvider.showApiKey" class="config-group">
            <label
                class="block text-sm font-medium mb-2"
            >
                API 密钥
            </label>
            <n-input
                :value="props.selectedProvider.apiKey"
                @update:value="setApiKey"
                placeholder="请输入 API 密钥"
                type="password"
                show-password-on="click"
                size="large"
                class="ios-input"
            />
            <p
                class="mt-2 text-xs cursor-pointer hover:underline"
                @click="openApiKeyUrl"
            >
                不知道如何获取密钥？
            </p>
        </div>
    </div>
</template>
<style scoped>
label, p {
    color: var(--color-text);
}
p.text-xs {
    color: var(--text-secondary-color);
}
</style>
