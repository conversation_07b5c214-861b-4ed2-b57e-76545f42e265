<script setup lang="ts">
import { ref, watch } from "vue";
import { useModelStore } from "@/store/useModelStore.ts";
import { NButton, NIcon, NModal, NInput } from "naive-ui";
import { EditOutlined } from "@vicons/antd";

interface Props {
    providerKey: string;
    displayName: string;
}

const props = defineProps<Props>();
const modelStore = useModelStore();

const showEditNameModal = ref(false);
const newProviderName = ref("");

watch(
    () => props.displayName,
    (newName) => {
        newProviderName.value = newName;
    }
);

const openEditNameModal = () => {
    newProviderName.value = props.displayName;
    showEditNameModal.value = true;
};

const handleUpdateProviderName = () => {
    if (newProviderName.value.trim()) {
        modelStore.updateProvider(props.providerKey, {
            displayName: newProviderName.value.trim(),
        });
        showEditNameModal.value = false;
    }
};
</script>

<template>
    <div class="flex items-center">
        <h2 class="text-xl font-semibold pr-1">
            {{ props.displayName }}
        </h2>
        <n-button @click="openEditNameModal" size="small" text class="ml-2">
            <template #icon>
                <n-icon><EditOutlined /></n-icon>
            </template>
        </n-button>
    </div>

    <n-modal
        v-model:show="showEditNameModal"
        preset="card"
        style="width: 400px"
        title="修改供应商名称"
    >
        <n-input
            v-model:value="newProviderName"
            placeholder="请输入新的供应商名称"
            @keyup.enter="handleUpdateProviderName"
        />
        <template #footer>
            <div class="flex justify-end space-x-2">
                <n-button @click="showEditNameModal = false">取消</n-button>
                <n-button type="primary" @click="handleUpdateProviderName">
                    保存
                </n-button>
            </div>
        </template>
    </n-modal>
</template>
