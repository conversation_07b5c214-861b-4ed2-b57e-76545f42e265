<script setup lang="ts">
import { computed } from "vue";
import { NButton, NIcon } from "naive-ui";
import { Add, CheckmarkCircle } from "@vicons/ionicons5";
import type { ProviderConfig } from "@/core/types/model.ts";

interface Props {
    providers: ProviderConfig[];
    selectedProviderId: string;
}

const props = defineProps<Props>();

const emit = defineEmits(["select-provider", "add-provider"]);

const isProviderActive = (provider: ProviderConfig) => {
    return !!provider.isActive;
};

const selectProvider = (providerId: string) => {
    emit("select-provider", providerId);
};

const openAddProviderModal = () => {
    emit("add-provider");
};
</script>

<template>
    <div
        class="provider-sidebar w-64 border-r flex flex-col"
    >
        <!-- 标题栏 -->
        <div class="p-4 border-b">
            <h3 class="text-lg font-semibold">
                供应商
            </h3>
        </div>

        <!-- 供应商列表 -->
        <div class="flex-1 overflow-y-auto">
            <div class="p-2 space-y-1">
                <div
                    v-for="provider in props.providers"
                    :key="provider.key"
                    @click="selectProvider(provider.key)"
                    class="provider-item flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200"
                    :class="{
                        'provider-item-selected':
                            props.selectedProviderId === provider.key,
                        'provider-item-hover':
                            props.selectedProviderId !== provider.key,
                    }"
                >
                    <div class="flex items-center space-x-3 flex-1">
                        <div class="flex-shrink-0">
                            <!-- 供应商图标或状态指示器 -->
                            <div
                                class="w-3 h-3 rounded-full"
                                :class="{
                                    'status-active': isProviderActive(provider),
                                    'status-inactive':
                                        !isProviderActive(provider),
                                }"
                            ></div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p
                                class="text-sm font-medium truncate"
                            >
                                {{ provider.displayName }}
                            </p>
                            <p class="text-xs">
                                点击配置
                            </p>
                        </div>
                    </div>
                    <n-icon
                        v-if="props.selectedProviderId === provider.key"
                        size="16"
                    >
                        <CheckmarkCircle />
                    </n-icon>
                </div>
            </div>
        </div>

        <!-- 添加供应商按钮 -->
        <div class="p-4 border-t">
            <n-button @click="openAddProviderModal" type="primary" block>
                <template #icon>
                    <n-icon><Add /></n-icon>
                </template>
                添加供应商
            </n-button>
        </div>
    </div>
</template>
<style scoped>
.provider-sidebar {
    background-color: var(--color-background);
    border-color: var(--border-color-primary);
}
.provider-item-selected {
    background-color: var(--side-active-bg-color);
    color: var(--side-text-color);
}
.provider-item-hover:hover {
    background-color: var(--side-hover-bg-color);
}
.status-active {
    background-color: #34d399;
}
.status-inactive {
    background-color: var(--text-secondary-color);
}
h3, p {
    color: var(--color-text);
}
p.text-xs {
    color: var(--text-secondary-color);
}
.border-b, .border-t {
    border-color: var(--border-color-primary);
}
</style>
