<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useModelStore } from "@/store/useModelStore.ts";
import { useMessage } from "naive-ui";
import AddProviderModal from "./AddProviderModal.vue";
import ProviderList from "./ProviderList.vue";
import ProviderConfigPanel from "./ProviderConfigPanel.vue";

const modelStore = useModelStore();
const message = useMessage();

// 当前选中的供应商
const selectedProviderId = ref<string>("");

// 供应商列表
const providers = computed(() => modelStore.providerList);

// 初始化选中的供应商
const initializeSelectedProvider = () => {
    if (providers.value.length > 0) {
        // 优先选择当前配置的默认供应商
        if (
            modelStore.provider &&
            providers.value.some((p) => p.key === modelStore.provider)
        ) {
            selectedProviderId.value = modelStore.provider;
        } else {
            // 如果没有默认供应商或找不到，则选择第一个供应商
            selectedProviderId.value = providers.value[0].key;
        }
    }
};

// 初始化
initializeSelectedProvider();

// 监听模型存储的默认供应商变化，自动更新选中的供应商
watch(
    () => modelStore.provider,
    (newProvider) => {
        if (newProvider && providers.value.some((p) => p.key === newProvider)) {
            selectedProviderId.value = newProvider;
        }
    }
);

// 监听供应商列表变化，确保选中的供应商有效
watch(
    providers,
    () => {
        if (
            !selectedProviderId.value ||
            !providers.value.some((p) => p.key === selectedProviderId.value)
        ) {
            initializeSelectedProvider();
        }
    },
    { immediate: true }
);

// 当前选中的供应商对象
const selectedProvider = computed(() => {
    return providers.value.find((p) => p.key === selectedProviderId.value);
});

// 添加供应商模态框状态
const addProviderModalShow = ref(false);

// 选择供应商
const selectProvider = (providerId: string) => {
    selectedProviderId.value = providerId;
};

// 打开添加供应商模态框
const openAddProviderModal = () => {
    addProviderModalShow.value = true;
};

// 处理从添加供应商模态框保存事件
const handleAddProviderSave = (name: string) => {
    const newProviderKey = modelStore.addProvider({
        name: name,
        baseUrl: "",
        apiKey: "",
        models: [],
    });
    message.success(`供应商 ${name} 添加成功`);
    addProviderModalShow.value = false;

    // 自动选中新添加的供应商
    if (newProviderKey) {
        selectedProviderId.value = newProviderKey;
    }
};

// 删除供应商
const handleRemoveProvider = (providerId: string, providerName: string) => {
    // 如果删除的是当前选中的供应商，切换到第一个供应商
    if (selectedProviderId.value === providerId) {
        const remainingProviders = providers.value.filter(
            (p) => p.key !== providerId
        );
        selectedProviderId.value =
            remainingProviders.length > 0 ? remainingProviders[0].key : "";
    }

    modelStore.removeProvider(providerId);
    message.success(`供应商 ${providerName} 已删除`);
};

// 切换供应商激活状态
const toggleProvider = async (providerId: string, checked: boolean) => {
    modelStore.setProviderActive(providerId, checked);

    // 如果是启用，则尝试更新该供应商的模型列表
    if (checked) {
        try {
            await modelStore.fetchProviderModels(providerId);
            console.log(
                `Successfully updated model list for ${providerId} on toggle.`
            );
        } catch (error) {
            console.warn(
                `Failed to update model list for ${providerId} on toggle:`,
                error
            );
            // The store now handles setting the error state, so no need to show a message here.
            // The UI will react to the error state in the provider config.
        }
    }
};
</script>

<template>
    <div
        class="ios-settings-container h-full flex bg-gray-50 dark:bg-transparent rounded-lg overflow-hidden"
    >
        <!-- 左侧供应商列表 -->
        <ProviderList
            :providers="providers"
            :selected-provider-id="selectedProviderId"
            @select-provider="selectProvider"
            @add-provider="openAddProviderModal"
        />

        <!-- 右侧配置面板 -->
        <ProviderConfigPanel
            v-if="selectedProvider"
            :selected-provider="selectedProvider"
            @remove-provider="handleRemoveProvider"
            @toggle-provider="toggleProvider"
        />

        <!-- 未选中供应商时的空状态 -->
        <div v-else class="flex-1 flex items-center justify-center">
            <div class="text-center">
                <p class="text-lg text-gray-500 dark:text-gray-400 mb-2">
                    请选择一个供应商
                </p>
                <p class="text-sm text-gray-400 dark:text-gray-500">
                    从左侧列表中选择要配置的供应商
                </p>
            </div>
        </div>

        <!-- 添加供应商模态框 -->
        <AddProviderModal
            v-model:show="addProviderModalShow"
            @save="handleAddProviderSave"
        />
    </div>
</template>

<style scoped>
.provider-section {
    transition: all 0.3s ease;
}
.ios-settings-container {
    background-color: var(--color-background-soft);
}
</style>
