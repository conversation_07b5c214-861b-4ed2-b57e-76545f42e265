<template>
    <div class="data-export-container">
        <p class="section-title">数据导出</p>
        <p class="setting-description mb-4">
            导出您的模型配置和会话历史记录为JSON格式，数据将复制到粘贴板，便于备份和迁移。
        </p>

        <div class="space-y-4">
            <!-- 模型配置导出 -->
            <div class="export-item">
                <div class="flex items-center justify-between">
                    <div>
                        <span class="setting-label">模型配置</span>
                        <p class="setting-description mt-1">
                            导出所有供应商配置信息，包括API密钥、BaseURL等设置
                        </p>
                    </div>
                    <n-button
                        @click="exportModelConfig"
                        type="primary"
                        size="small"
                        :loading="isExporting.modelConfig"
                    >
                        复制配置
                    </n-button>
                </div>
            </div>

            <!-- 会话历史记录导出 -->
            <div class="export-item">
                <div class="flex items-center justify-between">
                    <div>
                        <span class="setting-label">会话历史记录</span>
                        <p class="setting-description mt-1">
                            导出所有会话的聊天记录，包括消息内容、时间戳等信息
                        </p>
                    </div>
                    <n-button
                        @click="exportChatHistory"
                        type="primary"
                        size="small"
                        :loading="isExporting.chatHistory"
                    >
                        复制记录
                    </n-button>
                </div>
            </div>

            <!-- 完整数据导出 -->
            <div class="export-item">
                <div class="flex items-center justify-between">
                    <div>
                        <span class="setting-label">完整数据</span>
                        <p class="setting-description mt-1">
                            导出所有数据，包括模型配置和会话历史记录
                        </p>
                    </div>
                    <n-button
                        @click="exportAllData"
                        type="primary"
                        size="small"
                        :loading="isExporting.allData"
                    >
                        复制全部
                    </n-button>
                </div>
            </div>
        </div>

        <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p class="text-sm text-blue-700 dark:text-blue-300">
                <strong>注意：</strong>导出的数据包含敏感信息（如API密钥），请妥善保管，避免泄露。数据已复制到粘贴板，您可以粘贴到文本编辑器中保存。
            </p>
        </div>

        <!-- 手动复制对话框 -->
        <n-modal
            v-model:show="showManualCopyDialog"
            preset="dialog"
            title="手动复制数据"
            :style="{ width: '80%', maxWidth: '800px' }"
        >
            <div class="manual-copy-content">
                <p class="mb-4 text-orange-600 dark:text-orange-400">
                    <n-icon :component="WarningIcon" class="mr-2" />
                    自动复制失败，请手动选择下方内容并复制：
                </p>

                <n-input
                    v-model:value="exportDataForManualCopy"
                    type="textarea"
                    readonly
                    :rows="20"
                    placeholder="导出数据将显示在这里..."
                    class="manual-copy-textarea"
                    @click="selectAllText"
                />

                <div class="mt-4 flex justify-between items-center">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        点击文本框可全选内容，然后使用 Ctrl+C (Windows) 或 Cmd+C (Mac) 复制
                    </p>
                    <div class="flex gap-2">
                        <n-button @click="selectAllText" size="small">
                            全选
                        </n-button>
                        <n-button @click="showManualCopyDialog = false" size="small">
                            关闭
                        </n-button>
                    </div>
                </div>
            </div>
        </n-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { NButton, useMessage } from "naive-ui";
import { useModelStore } from "@/store/useModelStore.ts";
import { chatStoreAdapter } from "@/adapters/ChatStoreAdapter";

const message = useMessage();
const modelStore = useModelStore();

// 导出状态管理
const isExporting = reactive({
    modelConfig: false,
    chatHistory: false,
    allData: false
});

/**
 * 复制数据到粘贴板
 */
const copyToClipboard = async (data: string, successMessage: string) => {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            // 使用现代 Clipboard API
            await navigator.clipboard.writeText(data);
        } else {
            // 降级方案：使用传统的 document.execCommand
            const textArea = document.createElement("textarea");
            textArea.value = data;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);
            
            if (!successful) {
                throw new Error('复制失败');
            }
        }
        
        message.success(successMessage);
    } catch (error) {
        console.error("复制到粘贴板失败:", error);
        message.error("复制失败，请手动复制数据");
        
        // 如果复制失败，显示数据让用户手动复制
        console.log("导出数据:", data);
    }
};

/**
 * 导出模型配置
 */
const exportModelConfig = async () => {
    if (isExporting.modelConfig) return;
    
    isExporting.modelConfig = true;
    try {
        const config = {
            providers: modelStore.providerList,
            exportTime: new Date().toISOString(),
            version: "1.0.0",
            type: "model-config"
        };

        const dataStr = JSON.stringify(config, null, 2);
        await copyToClipboard(dataStr, "模型配置已复制到粘贴板");
    } catch (error) {
        console.error("导出模型配置失败:", error);
        message.error("导出模型配置失败");
    } finally {
        isExporting.modelConfig = false;
    }
};

/**
 * 导出会话历史记录
 */
const exportChatHistory = async () => {
    if (isExporting.chatHistory) return;
    
    isExporting.chatHistory = true;
    try {
        // 使用适配器导出聊天历史
        const chatsWithMessages = await chatStoreAdapter.exportChatHistory();

        const chatHistory = {
            chats: chatsWithMessages,
            exportTime: new Date().toISOString(),
            version: "1.0.0",
            type: "chat-history"
        };

        const dataStr = JSON.stringify(chatHistory, null, 2);
        await copyToClipboard(dataStr, "会话历史记录已复制到粘贴板");
    } catch (error) {
        console.error("导出会话历史记录失败:", error);
        message.error("导出会话历史记录失败");
    } finally {
        isExporting.chatHistory = false;
    }
};

/**
 * 导出所有数据
 */
const exportAllData = async () => {
    if (isExporting.allData) return;
    
    isExporting.allData = true;
    try {
        // 使用适配器导出聊天历史
        const chatsWithMessages = await chatStoreAdapter.exportChatHistory();

        const allData = {
            modelConfig: {
                providers: modelStore.providerList,
            },
            chatHistory: {
                chats: chatsWithMessages,
            },
            exportTime: new Date().toISOString(),
            version: "1.0.0",
            type: "complete-data"
        };

        const dataStr = JSON.stringify(allData, null, 2);
        await copyToClipboard(dataStr, "完整数据已复制到粘贴板");
    } catch (error) {
        console.error("导出所有数据失败:", error);
        message.error("导出所有数据失败");
    } finally {
        isExporting.allData = false;
    }
};
</script>

<style scoped>
.data-export-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* 标题样式 */
.section-title {
    font-weight: bold;
    color: var(--color-text);
    margin-bottom: 0.5rem;
}

/* 设置标签 */
.setting-label {
    display: block;
    color: var(--color-text);
    font-size: 0.875rem;
}

/* 描述文字 */
.setting-description {
    font-size: 0.75rem;
    color: var(--text-secondary-color);
    margin-top: 0.5rem;
    line-height: 1.5;
}

/* 导出项样式 */
.export-item {
    padding: 1rem;
    border: 1px solid var(--border-color-primary);
    border-radius: 8px;
    background-color: var(--color-background);
    transition: all 0.2s ease;
}

.export-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
