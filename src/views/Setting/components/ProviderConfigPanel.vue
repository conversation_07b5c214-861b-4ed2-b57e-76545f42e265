<script setup lang="ts">
import { computed } from "vue";
import { useModelStore } from "@/store/useModelStore.ts";
import { NButton, NIcon, NSwitch, NAlert } from "naive-ui";
import { DeleteFilled } from "@vicons/antd";
import type { ProviderConfig } from "@/core/types/model.ts";
import ProviderBasicConfig from "./ProviderBasicConfig.vue";
import ModelManager from "./ModelManager.vue";
import EditProviderName from "./EditProviderName.vue";

interface Props {
    selectedProvider: ProviderConfig;
}

const props = defineProps<Props>();
const emit = defineEmits(["remove-provider", "toggle-provider"]);

const modelStore = useModelStore();

const isProviderActive = computed(() => {
    return !!props.selectedProvider.isActive;
});

const handleRemoveProvider = () => {
    emit(
        "remove-provider",
        props.selectedProvider.key,
        props.selectedProvider.displayName
    );
};

const toggleProvider = (checked: boolean) => {
    emit("toggle-provider", props.selectedProvider.key, checked);
};
</script>

<template>
    <div class="provider-config flex-1 flex flex-col">
        <!-- 配置标题栏 -->
        <div class="p-6 border-b">
            <div class="flex items-center justify-between">
                <EditProviderName
                    :provider-key="props.selectedProvider.key"
                    :display-name="props.selectedProvider.displayName"
                />
                <div class="flex items-center space-x-3 gap-2">
                    <!-- 删除按钮 -->
                    <n-button
                        v-if="props.selectedProvider.showDelete"
                        @click="handleRemoveProvider"
                        size="small"
                        circle
                        type="error"
                        secondary
                    >
                        <template #icon>
                            <n-icon><DeleteFilled /></n-icon>
                        </template>
                    </n-button>
                    <!-- 启用/禁用开关 -->
                    <div class="flex items-center space-x-2">
                        <span class="text-sm">启用</span>
                        <n-switch
                            :value="isProviderActive"
                            @update:value="toggleProvider"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置表单区域 -->
        <div class="flex-1 overflow-y-auto p-6">
            <div class="max-w-2xl space-y-6">
                <ProviderBasicConfig
                    :selected-provider="props.selectedProvider"
                />
                <ModelManager
                    :selected-provider="props.selectedProvider"
                />
            </div>
        </div>
    </div>
</template>
<style scoped>
.provider-config {
    background-color: var(--color-background-soft);
}
.border-b {
    border-color: var(--border-color-primary);
}
h2,
span,
p {
    color: var(--color-text);
}
p.text-sm,
span.text-sm {
    color: var(--text-secondary-color);
}
.disabled-provider-notice {
    background-color: var(--color-background-mute);
    border: 1px solid var(--border-color-primary);
}
</style>
