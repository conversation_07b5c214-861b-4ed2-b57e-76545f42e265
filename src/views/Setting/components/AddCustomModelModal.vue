<script setup lang="ts">
import { ref } from "vue";
import { NButton, NInput, NModal, useMessage } from "naive-ui";

interface Props {
    show: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(["update:show", "add-model"]);
const message = useMessage();

const customModelName = ref("");

const handleAddModel = () => {
    if (!customModelName.value.trim()) {
        message.warning("模型名称不能为空");
        return;
    }
    emit("add-model", customModelName.value.trim());
    emit("update:show", false);
};

const handleClose = () => {
    emit("update:show", false);
};
</script>

<template>
    <n-modal
        :show="props.show"
        preset="card"
        style="width: 400px"
        title="添加自定义模型"
        :mask-closable="false"
        @update:show="handleClose"
        :on-after-leave="() => (customModelName = '')"
    >
        <n-input
            v-model:value="customModelName"
            placeholder="请输入模型名称"
            @keyup.enter="handleAddModel"
        />
        <template #footer>
            <div class="flex justify-end space-x-2 gap-2">
                <n-button @click="handleClose">取消</n-button>
                <n-button type="primary" @click="handleAddModel">
                    确定
                </n-button>
            </div>
        </template>
    </n-modal>
</template>
