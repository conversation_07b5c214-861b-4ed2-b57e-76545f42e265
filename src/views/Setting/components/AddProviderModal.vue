<script setup lang="ts">
import { ref, watch } from "vue";
import {
    useMessage,
    NButton,
    NInput,
    NForm,
    NFormItem,
    NModal,
    NCard,
    NSpace,
} from "naive-ui";

interface Props {
    show: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    show: false,
});

const emit = defineEmits<{
    (e: "update:show", value: boolean): void;
    (e: "save", name: string): void;
}>();

const message = useMessage();
const providerName = ref("");

// 监听模态框显示状态，重置表单
watch(
    () => props.show,
    (newVal) => {
        if (newVal) {
            providerName.value = "";
        }
    }
);

const handleSave = () => {
    const name = providerName.value.trim();
    if (!name) {
        message.warning("请输入供应商名称");
        return;
    }
    emit("save", name);
};

const handleClose = () => {
    emit("update:show", false);
};
</script>

<template>
    <n-modal :show="props.show" @update:show="handleClose">
        <n-card
            style="width: 400px"
            title="添加自定义供应商"
            :bordered="false"
            size="huge"
            role="dialog"
            aria-modal="true"
        >
            <n-form>
                <n-form-item label="供应商名称" required>
                    <n-input
                        v-model:value="providerName"
                        placeholder="例如：My OpenAI Service"
                        @keydown.enter="handleSave"
                    />
                    <template #feedback>
                        创建后可在右侧面板配置 Base URL、API Key 和模型。
                    </template>
                </n-form-item>
            </n-form>
            <template #footer>
                <n-space justify="end">
                    <n-button @click="handleClose">取消</n-button>
                    <n-button type="primary" @click="handleSave">创建</n-button>
                </n-space>
            </template>
        </n-card>
    </n-modal>
</template>
