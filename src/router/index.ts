import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import ChatPage from '@/views/Chat/ChatPage.vue';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Layout',
    component: ChatPage,
  },
  {
    path: '/setting',
    name: 'Setting',
    component: () => import('@/views/Setting/SettingPage.vue'),
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

export default router;
