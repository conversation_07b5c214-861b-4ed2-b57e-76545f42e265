import <PERSON>ie, { Table } from "dexie";
import type {
    Msg,
    MessageContent,
    MessageRecord,
    MessageRole,
    MessageStatus,
} from "@/core/types/chat.ts";
import { chatDb } from "./chat.db";

// 消息数据库类
export class MessageDatabase extends <PERSON><PERSON> {
    // 定义表
    messages!: Table<MessageRecord, string>;

    constructor() {
        super("MessageDatabase");

        // 定义数据库结构
        this.version(1).stores({
            messages: "id, chatId, role, timestamp, [chatId+timestamp]",
        });

        // 版本2：添加 thinkContent 字段支持
        this.version(2).stores({
            messages: "id, chatId, role, timestamp, [chatId+timestamp]",
        }).upgrade(tx => {
            // 数据库升级时不需要特殊处理，新字段会自动支持
            console.log("升级消息数据库到版本2，添加思考内容支持");
        });
    }

    // 添加消息
    async addMessage(message: MessageRecord): Promise<string> {
        await this.messages.put(message);

        // 更新聊天的预览和消息计数
        await this.updateChatStats(message.chatId);

        return message.id;
    }

    // 获取消息
    async getMessage(id: string): Promise<MessageRecord | undefined> {
        return await this.messages.get(id);
    }

    // 获取特定聊天的所有消息
    async getChatMessages(chatId: string): Promise<MessageRecord[]> {
        return await this.messages
            .where("chatId")
            .equals(chatId)
            .sortBy("timestamp");
    }

    // 更新消息
    async updateMessage(
        id: string,
        changes: Partial<Omit<MessageRecord, "id" | "chatId">>
    ): Promise<void> {
        await this.messages.update(id, changes);

        // 如果更新了content，可能需要更新聊天的预览
        if (changes.content) {
            const message = await this.getMessage(id);
            if (message) {
                await this.updateChatStats(message.chatId);
            }
        }
    }

    // 删除消息
    async deleteMessage(id: string): Promise<void> {
        const message = await this.getMessage(id);
        if (message) {
            const chatId = message.chatId;

            await this.messages.delete(id);

            // 更新聊天的预览和消息计数
            await this.updateChatStats(chatId);
        }
    }

    // 删除特定聊天的所有消息
    async deleteChatMessages(chatId: string): Promise<void> {
        await this.messages.where("chatId").equals(chatId).delete();

        // 更新聊天记录
        await chatDb.updateMessageStats(chatId, 0, "");
    }

    // 从Msg对象创建消息记录
    async createMessageFromObject(chatId: string, msg: Msg): Promise<string> {
        const messageRecord: MessageRecord = {
            id: msg.id,
            chatId,
            content: msg.content,
            short: msg.short,
            role: msg.role,
            model: msg.model,
            status: msg.status,
            timestamp: msg.timestamp || Date.now(),
            thinkContent: msg.thinkContent,
        };

        return await this.addMessage(messageRecord);
    }

    // 辅助方法：生成消息预览文本
    private getPreviewText(content: MessageContent): string {
        if (typeof content === "string") {
            return content;
        } else if (Array.isArray(content)) {
            // 从数组中找到第一个字符串作为预览
            const firstText = content.find((item) => typeof item === "string");
            return firstText ? String(firstText) : "[多媒体消息]";
        } else if (
            content &&
            typeof content === "object" &&
            content.type === "image"
        ) {
            return "[图片消息]";
        }
        return "[消息]";
    }

    // 更新聊天统计信息
    private async updateChatStats(chatId: string): Promise<void> {
        // 获取该聊天的所有消息
        const messages = await this.getChatMessages(chatId);

        // 更新聊天的消息计数和最后一条消息预览
        const messageCount = messages.length;
        let lastMsgPreview = "";

        if (messageCount > 0) {
            const lastMessage = messages[messageCount - 1];
            const previewText = this.getPreviewText(lastMessage.content);
            lastMsgPreview =
                previewText.slice(0, 20) +
                (previewText.length > 20 ? "..." : "");
        }

        // 更新聊天记录
        await chatDb.updateMessageStats(chatId, messageCount, lastMsgPreview);
    }
}

// 导出单例实例
export const messageDb = new MessageDatabase();
