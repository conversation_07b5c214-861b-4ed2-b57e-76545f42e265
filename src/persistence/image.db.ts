import Dexie, { Table } from "dexie";
import type { ImageRecord } from "@/core/types/chat.ts";

// 图片数据库类
export class ImageDatabase extends <PERSON><PERSON> {
    // 定义表
    images!: Table<ImageRecord, number>;

    constructor() {
        super("ImageDatabase");

        // 定义数据库结构
        this.version(1).stores({
            images: "++id, name, mimeType, createdAt",
            // ++id 表示自增主键
        });
    }

    // 添加图片
    async addImage(
        image: Omit<ImageRecord, "id" | "createdAt">
    ): Promise<number> {
        const record: ImageRecord = {
            ...image,
            createdAt: new Date(),
        };
        return await this.images.add(record);
    }

    // 根据 ID 获取图片
    async getImage(id: number): Promise<ImageRecord | undefined> {
        return await this.images.get(id);
    }

    // 获取所有图片信息（不包括 blob 数据，避免内存占用过大）
    async getAllImageInfo(): Promise<Omit<ImageRecord, "blob">[]> {
        const allImages = await this.images.toArray();
        return allImages.map((image: ImageRecord) => {
            const { blob, ...imageInfo } = image;
            return imageInfo;
        });
    }

    // 更新图片
    async updateImage(
        id: number,
        changes: Partial<Omit<ImageRecord, "id">>
    ): Promise<number> {
        return await this.images.update(id, changes);
    }

    // 删除图片
    async deleteImage(id: number): Promise<void> {
        await this.images.delete(id);
    }

    // 清空所有图片
    async clearImages(): Promise<void> {
        await this.images.clear();
    }

    // 通过 blob URL 获取图片
    async getBlobUrl(id: number): Promise<string | null> {
        const image = await this.getImage(id);
        if (!image) return null;
        return URL.createObjectURL(image.blob);
    }

    // 释放 blob URL
    releaseBlobUrl(url: string): void {
        URL.revokeObjectURL(url);
    }
}

// 导出单例实例
export const imageDb = new ImageDatabase();

// 实用方法：从文件对象创建图片记录
export async function createImageRecordFromFile(
    file: File
): Promise<Omit<ImageRecord, "id" | "createdAt">> {
    return {
        name: file.name,
        blob: new Blob([file], { type: file.type }),
        mimeType: file.type,
        size: file.size,
    };
}
