import Dexie, { Table } from "dexie";
import { v4 as uuid } from "uuid";
import type { Chat, ChatRecord } from "@/core/types/chat.ts";

// 聊天数据库类 - 只负责会话管理
export class ChatDatabase extends Dexie {
    // 定义表
    chats!: Table<ChatRecord, string>;

    constructor() {
        super("ChatDatabase");

        // 定义数据库结构 - 只有会话表
        this.version(1).stores({
            chats: "id, time, updated",
        });
    }

    // ===== 聊天会话相关方法 =====

    // 添加聊天
    async addChat(chat: Omit<ChatRecord, "updated">): Promise<string> {
        const record: ChatRecord = {
            ...chat,
            updated: Date.now(),
        };
        await this.chats.put(record);
        return record.id;
    }

    // 获取聊天
    async getChat(id: string): Promise<ChatRecord | undefined> {
        return await this.chats.get(id);
    }

    // 获取所有聊天
    async getAllChats(): Promise<ChatRecord[]> {
        return await this.chats.orderBy("updated").reverse().toArray();
    }

    // 更新聊天
    async updateChat(
        id: string,
        changes: Partial<Omit<ChatRecord, "id">>
    ): Promise<void> {
        await this.chats.update(id, {
            ...changes,
            updated: Date.now(),
        });
    }

    // 删除聊天 - 不再负责删除消息，只删除会话
    async deleteChat(id: string): Promise<void> {
        await this.chats.delete(id);
    }

    // 创建一个新的聊天记录
    async createNewChat(short: string = ""): Promise<ChatRecord> {
        const chatId = uuid();
        const chat: ChatRecord = {
            id: chatId,
            short: short || "", // 使用空字符串，这样可以优先显示lastMsgPreview
            time: Date.now(),
            updated: Date.now(),
            lastMsgPreview: "新的对话", // 默认显示“新的对话”
            messageCount: 0,
        };

        await this.addChat(chat);
        return chat;
    }

    // 从Chat对象创建聊天记录
    async createChatFromObject(chat: Chat): Promise<string> {
        const chatRecord: ChatRecord = {
            id: chat.id,
            short: chat.short,
            time: chat.time,
            updated: Date.now(),
            lastMsgPreview: chat.lastMsgPreview || "",
            messageCount: chat.messageCount || 0,
        };

        return await this.addChat(chatRecord);
    }

    // 更新消息计数和预览
    async updateMessageStats(
        chatId: string,
        messageCount: number,
        lastMsgPreview: string
    ): Promise<void> {
        await this.updateChat(chatId, {
            messageCount,
            lastMsgPreview,
        });
    }
}

// 导出单例实例
export const chatDb = new ChatDatabase();
