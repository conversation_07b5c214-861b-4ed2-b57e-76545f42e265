import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SendMessageUseCase } from '../message/SendMessageUseCase';
import { MessageStatus } from '@/core/types/chat';
import type { IMessageRepository } from '@/infrastructure/MessageRepository';
import type { IChatRepository } from '@/infrastructure/ChatRepository';
import type { IAIProvider } from '@/infrastructure/AIProvider';

// Mock implementations
class MockMessageRepository implements IMessageRepository {
  private messages = new Map();

  async create(params: any) {
    const message = {
      id: params.id || 'test-msg-id',
      chatId: params.chatId,
      content: params.content,
      role: params.role,
      status: params.status || MessageStatus.IDLE,
      timestamp: Date.now(),
      short: typeof params.content === 'string' ? params.content.slice(0, 50) : 'Message',
    };
    this.messages.set(message.id, message);
    return message;
  }

  async update(params: any) {
    const message = this.messages.get(params.id);
    if (message) {
      Object.assign(message, params.updates);
    }
  }

  async findById(messageId: string) {
    return this.messages.get(messageId);
  }

  async findByChatId(chatId: string) {
    return Array.from(this.messages.values()).filter(msg => msg.chatId === chatId);
  }

  async delete(messageId: string) {
    this.messages.delete(messageId);
  }
}

class MockChatRepository implements IChatRepository {
  private chats = new Map();

  async findById(chatId: string) {
    return this.chats.get(chatId) || {
      id: chatId,
      short: 'Test Chat',
      time: Date.now(),
      messageCount: 0,
      lastMsgPreview: '',
      msgList: [],
    };
  }

  async create(chat: any) {
    const newChat = {
      id: chat.id || 'test-chat-id',
      short: chat.short || 'New Chat',
      time: Date.now(),
      messageCount: 0,
      lastMsgPreview: '',
      msgList: [],
    };
    this.chats.set(newChat.id, newChat);
    return newChat;
  }

  async update(chatId: string, updates: any) {
    const chat = this.chats.get(chatId);
    if (chat) {
      Object.assign(chat, updates);
    }
    return chat;
  }

  async delete(chatId: string) {
    this.chats.delete(chatId);
  }

  async findAll() {
    return Array.from(this.chats.values());
  }
}

class MockAIProvider implements IAIProvider {
  async sendMessage(params: any): Promise<AsyncIterable<string>> {
    const chunks = ['Mock ', 'AI ', 'stream ', 'response'];

    return {
      async *[Symbol.asyncIterator]() {
        for (const chunk of chunks) {
          yield chunk;
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }
    };
  }

  async generateResponse(messages: any[], options: any) {
    return {
      content: 'Mock AI response',
      model: 'mock-model',
      usage: { tokens: 100 },
    };
  }

  async generateStreamResponse(messages: any[], options: any) {
    // Mock stream response
    const chunks = ['Mock ', 'AI ', 'stream ', 'response'];
    let index = 0;

    return {
      async *[Symbol.asyncIterator]() {
        for (const chunk of chunks) {
          yield { content: chunk, done: false };
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        yield { content: '', done: true };
      }
    };
  }

  async getModels() {
    return [{ id: 'mock-model', name: 'Mock Model' }];
  }
}

class MockProcessThinkingUseCase {
  async execute(params: any) {
    return {
      success: true,
      processedContent: params.content,
      thinkContent: undefined,
    };
  }
}

class MockUpdateMessageUseCase {
  async execute(params: any) {
    return {
      success: true,
      message: params.message,
    };
  }
}

describe('SendMessageUseCase', () => {
  let useCase: SendMessageUseCase;
  let mockMessageRepo: MockMessageRepository;
  let mockChatRepo: MockChatRepository;
  let mockAIProvider: MockAIProvider;
  let mockProcessThinking: MockProcessThinkingUseCase;
  let mockUpdateMessage: MockUpdateMessageUseCase;

  beforeEach(() => {
    mockMessageRepo = new MockMessageRepository();
    mockChatRepo = new MockChatRepository();
    mockAIProvider = new MockAIProvider();
    mockProcessThinking = new MockProcessThinkingUseCase();
    mockUpdateMessage = new MockUpdateMessageUseCase();

    useCase = new SendMessageUseCase(
      mockMessageRepo,
      mockAIProvider,
      mockProcessThinking as any,
      {} as any, // sessionManager mock
      mockUpdateMessage as any
    );
  });

  describe('execute', () => {
    it('should create user message and AI response', async () => {
      const input = {
        chatId: 'test-chat-id',
        content: 'Hello, AI!',
        onUserMessageCreated: vi.fn(),
        onAssistantMessageCreated: vi.fn(),
        onMessageUpdate: vi.fn(),
      };

      const result = await useCase.execute(input);

      expect(result.success).toBe(true);
      expect(result.userMessage).toBeDefined();
      expect(result.assistantMessage).toBeDefined();
      expect(result.userMessage?.content).toBe('Hello, AI!');
      expect(result.userMessage?.role).toBe('user');
      expect(result.assistantMessage?.role).toBe('assistant');
      expect(input.onUserMessageCreated).toHaveBeenCalledWith(result.userMessage);
      expect(input.onAssistantMessageCreated).toHaveBeenCalled();
    });

    it('should handle empty content', async () => {
      const input = {
        chatId: 'test-chat-id',
        content: '',
      };

      const result = await useCase.execute(input);

      expect(result.success).toBe(false);
    });

    it('should handle resend functionality', async () => {
      const input = {
        chatId: 'test-chat-id',
        content: 'Resend this message',
        resend: true,
        assistantMessageId: 'existing-msg-id',
      };

      const result = await useCase.execute(input);

      expect(result.success).toBe(true);
      expect(result.userMessage).toBeDefined();
      expect(result.assistantMessage).toBeDefined();
    });

    it('should handle AI provider errors', async () => {
      // Mock AI provider to throw error
      mockAIProvider.generateStreamResponse = vi.fn().mockRejectedValue(
        new Error('AI service unavailable')
      );

      const input = {
        chatId: 'test-chat-id',
        content: 'This will fail',
      };

      const result = await useCase.execute(input);

      expect(result.success).toBe(false);
    });

    it('should call callbacks during stream processing', async () => {
      const onMessageUpdate = vi.fn();
      const input = {
        chatId: 'test-chat-id',
        content: 'Stream test',
        onMessageUpdate,
      };

      await useCase.execute(input);

      // Should be called multiple times during streaming
      expect(onMessageUpdate).toHaveBeenCalled();
      expect(onMessageUpdate.mock.calls.length).toBeGreaterThan(1);
    });

    it('should handle thinking content correctly', async () => {
      // Mock AI provider to return thinking content
      mockAIProvider.generateStreamResponse = vi.fn().mockImplementation(async function* () {
        yield { content: '<think>Let me think about this...</think>', done: false };
        yield { content: 'Here is my response', done: false };
        yield { content: '', done: true };
      });

      const onMessageUpdate = vi.fn();
      const input = {
        chatId: 'test-chat-id',
        content: 'Question requiring thinking',
        onMessageUpdate,
      };

      const result = await useCase.execute(input);

      expect(result.success).toBe(true);
      expect(result.assistantMessage?.thinkContent).toContain('Let me think about this');
      expect(result.assistantMessage?.content).toContain('Here is my response');
    });

    it('should update message status correctly during processing', async () => {
      const statusUpdates: any[] = [];
      const onMessageUpdate = vi.fn((msg) => {
        statusUpdates.push(msg.status);
      });

      const input = {
        chatId: 'test-chat-id',
        content: 'Status test',
        onMessageUpdate,
      };

      await useCase.execute(input);

      // Should progress through statuses: WAITING -> GENERATING -> COMPLETED
      expect(statusUpdates).toContain(MessageStatus.WAITING);
      expect(statusUpdates).toContain(MessageStatus.GENERATING);
      expect(statusUpdates[statusUpdates.length - 1]).toBe(MessageStatus.COMPLETED);
    });

    it('should handle concurrent executions', async () => {
      const input1 = {
        chatId: 'test-chat-1',
        content: 'Message 1',
      };

      const input2 = {
        chatId: 'test-chat-2',
        content: 'Message 2',
      };

      const [result1, result2] = await Promise.all([
        useCase.execute(input1),
        useCase.execute(input2),
      ]);

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      expect(result1.userMessage?.content).toBe('Message 1');
      expect(result2.userMessage?.content).toBe('Message 2');
    });

    it('should validate input parameters', async () => {
      const invalidInputs = [
        { chatId: '', content: 'test' },
        { chatId: 'test', content: null },
        { chatId: 'test', content: undefined },
      ];

      for (const input of invalidInputs) {
        const result = await useCase.execute(input as any);
        expect(result.success).toBe(false);
        expect(result.success).toBe(false);
      }
    });

    it('should handle large content', async () => {
      const largeContent = 'A'.repeat(10000);
      const input = {
        chatId: 'test-chat-id',
        content: largeContent,
      };

      const result = await useCase.execute(input);

      expect(result.success).toBe(true);
      expect(result.userMessage?.content).toBe(largeContent);
    });

    it('should handle special characters in content', async () => {
      const specialContent = '特殊字符 🚀 <script>alert("test")</script> \n\t\r';
      const input = {
        chatId: 'test-chat-id',
        content: specialContent,
      };

      const result = await useCase.execute(input);

      expect(result.success).toBe(true);
      expect(result.userMessage?.content).toBe(specialContent);
    });
  });

  describe('error handling', () => {
    it('should handle repository errors gracefully', async () => {
      mockMessageRepo.create = vi.fn().mockRejectedValue(new Error('Database error'));

      const input = {
        chatId: 'test-chat-id',
        content: 'This will cause DB error',
      };

      const result = await useCase.execute(input);

      expect(result.success).toBe(false);
    });

    it('should handle network timeouts', async () => {
      mockAIProvider.generateStreamResponse = vi.fn().mockImplementation(async function* () {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Simulate timeout
        yield { content: 'Too late', done: true };
      });

      const input = {
        chatId: 'test-chat-id',
        content: 'Timeout test',
      };

      // This test would need timeout handling in the actual implementation
      const result = await useCase.execute(input);
      
      // For now, just ensure it doesn't crash
      expect(result).toBeDefined();
    });
  });
});
