import { UseCase } from '@/usecases/base/UseCase';
import { IChatRepository } from '@/infrastructure/ChatRepository';
import { Chat } from '@/core/types/chat';

export interface UpdateChatInput {
  chatId: string;
  updates: Partial<Chat>;
}

export interface UpdateChatOutput {
  success: boolean;
}

export class UpdateChatUseCase extends UseCase<UpdateChatInput, UpdateChatOutput> {
  constructor(private chatRepo: IChatRepository) {
    super();
  }

  async execute(input: UpdateChatInput): Promise<UpdateChatOutput> {
    this.validateInput(input);

    try {
      await this.chatRepo.update(input.chatId, input.updates);

      return {
        success: true,
      };
    } catch (error) {
      console.error('Error updating chat:', error);
      throw error;
    }
  }

  private validateInput(input: UpdateChatInput) {
    if (!input.chatId) {
      throw new Error('Chat ID is required');
    }
    
    if (!input.updates || Object.keys(input.updates).length === 0) {
      throw new Error('Updates are required');
    }
  }
}
