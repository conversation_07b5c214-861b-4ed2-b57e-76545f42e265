import { UseCase } from '@/usecases/base/UseCase';
import { IChatRepository } from '@/infrastructure/ChatRepository';
import { Chat } from '@/core/types/chat';

export interface CreateChatInput {
  title?: string;
}

export interface CreateChatOutput {
  chat: Chat;
  success: boolean;
}

export class CreateChatUseCase extends UseCase<CreateChatInput, CreateChatOutput> {
  constructor(private chatRepo: IChatRepository) {
    super();
  }

  async execute(input: CreateChatInput): Promise<CreateChatOutput> {
    try {
      const chat = await this.chatRepo.create({
        short: input.title || '新会话',
      });

      return {
        chat,
        success: true,
      };
    } catch (error) {
      console.error('Error creating chat:', error);
      throw error;
    }
  }
}