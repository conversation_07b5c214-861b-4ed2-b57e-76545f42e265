import { IMessageRepository } from '@/infrastructure/MessageRepository';
import { IChatRepository } from '@/infrastructure/ChatRepository';
import { IAIProvider } from '@/infrastructure/AIProvider';
import { MessageRepositoryImpl } from '@/infrastructure/impl/MessageRepositoryImpl';
import { ChatRepositoryImpl } from '@/infrastructure/impl/ChatRepositoryImpl';
import { AIProviderImpl } from '@/infrastructure/impl/AIProviderImpl';

import { SendMessageUseCase } from '@/usecases/message/SendMessageUseCase';
import { ProcessThinkingUseCase } from '@/usecases/message/ProcessThinkingUseCase';
import { UpdateMessageUseCase } from '@/usecases/message/UpdateMessageUseCase';
import { ResendMessageUseCase } from '@/usecases/message/ResendMessageUseCase';
import { CreateChatUseCase } from '@/usecases/chat/CreateChatUseCase';
import { LoadChatUseCase } from '@/usecases/chat/LoadChatUseCase';
import { RemoveChatUseCase } from '@/usecases/chat/RemoveChatUseCase';
import { UpdateChatUseCase } from '@/usecases/chat/UpdateChatUseCase';
import { LoadAllChatsUseCase } from '@/usecases/chat/LoadAllChatsUseCase';
import { SummaryUseCase, GenerateSummaryUseCase } from '@/usecases/ai/SummaryUseCase';

/**
 * 依赖注入容器 - 管理所有用例和基础设施层的实例
 */
export class DIContainer {
  private static instance: DIContainer;
  
  // 基础设施层实例
  private _messageRepository: IMessageRepository;
  private _chatRepository: IChatRepository;
  private _aiProvider: IAIProvider;
  
  // 用例实例
  private _sendMessageUseCase: SendMessageUseCase;
  private _processThinkingUseCase: ProcessThinkingUseCase;
  private _updateMessageUseCase: UpdateMessageUseCase;
  private _resendMessageUseCase: ResendMessageUseCase;
  private _createChatUseCase: CreateChatUseCase;
  private _loadChatUseCase: LoadChatUseCase;
  private _removeChatUseCase: RemoveChatUseCase;
  private _updateChatUseCase: UpdateChatUseCase;
  private _loadAllChatsUseCase: LoadAllChatsUseCase;
  private _summaryUseCase: SummaryUseCase;

  private constructor() {
    // 初始化基础设施层
    this._messageRepository = new MessageRepositoryImpl();
    this._chatRepository = new ChatRepositoryImpl();
    this._aiProvider = new AIProviderImpl();
    
    // 初始化用例
    this._processThinkingUseCase = new ProcessThinkingUseCase();
    this._updateMessageUseCase = new UpdateMessageUseCase(this._messageRepository);
    this._createChatUseCase = new CreateChatUseCase(this._chatRepository);
    this._loadChatUseCase = new LoadChatUseCase(this._chatRepository, this._messageRepository);
    this._removeChatUseCase = new RemoveChatUseCase(this._chatRepository);
    this._updateChatUseCase = new UpdateChatUseCase(this._chatRepository);
    this._loadAllChatsUseCase = new LoadAllChatsUseCase();
    this._summaryUseCase = new SummaryUseCase();
    this._sendMessageUseCase = new SendMessageUseCase(
      this._messageRepository,
      this._aiProvider,
      this._chatRepository,
      this._processThinkingUseCase,
      this._updateMessageUseCase
    );
    this._resendMessageUseCase = new ResendMessageUseCase(
      this._messageRepository,
      this._sendMessageUseCase
    );
  }

  public static getInstance(): DIContainer {
    if (!DIContainer.instance) {
      DIContainer.instance = new DIContainer();
    }
    return DIContainer.instance;
  }

  // Getters for repositories
  get messageRepository(): IMessageRepository {
    return this._messageRepository;
  }

  get chatRepository(): IChatRepository {
    return this._chatRepository;
  }

  get aiProvider(): IAIProvider {
    return this._aiProvider;
  }

  // Getters for use cases
  get sendMessageUseCase(): SendMessageUseCase {
    return this._sendMessageUseCase;
  }

  get processThinkingUseCase(): ProcessThinkingUseCase {
    return this._processThinkingUseCase;
  }

  get updateMessageUseCase(): UpdateMessageUseCase {
    return this._updateMessageUseCase;
  }

  get createChatUseCase(): CreateChatUseCase {
    return this._createChatUseCase;
  }

  get loadChatUseCase(): LoadChatUseCase {
    return this._loadChatUseCase;
  }

  get removeChatUseCase(): RemoveChatUseCase {
    return this._removeChatUseCase;
  }

  get updateChatUseCase(): UpdateChatUseCase {
    return this._updateChatUseCase;
  }

  get loadAllChatsUseCase(): LoadAllChatsUseCase {
    return this._loadAllChatsUseCase;
  }

  get resendMessageUseCase(): ResendMessageUseCase {
    return this._resendMessageUseCase;
  }

  get summaryUseCase(): SummaryUseCase {
    return this._summaryUseCase;
  }
}

// 导出单例实例
export const diContainer = DIContainer.getInstance();
