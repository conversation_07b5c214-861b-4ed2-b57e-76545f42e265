/**
 * 性能指标接口
 */
interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  category: 'response_time' | 'memory' | 'cache' | 'throughput' | 'error';
  tags?: Record<string, string>;
}

/**
 * 性能统计信息
 */
interface PerformanceStats {
  min: number;
  max: number;
  avg: number;
  p50: number;
  p90: number;
  p95: number;
  p99: number;
  count: number;
  sum: number;
}

/**
 * 性能报告
 */
interface PerformanceReport {
  timeRange: {
    start: number;
    end: number;
    duration: number;
  };
  metrics: {
    responseTime: PerformanceStats;
    memoryUsage: PerformanceStats;
    cacheHitRate: number;
    throughput: number;
    errorRate: number;
  };
  alerts: Array<{
    level: 'warning' | 'critical';
    message: string;
    metric: string;
    value: number;
    threshold: number;
  }>;
  recommendations: string[];
}

/**
 * 性能阈值配置
 */
interface PerformanceThresholds {
  responseTime: {
    warning: number;
    critical: number;
  };
  memoryUsage: {
    warning: number;
    critical: number;
  };
  cacheHitRate: {
    warning: number;
    critical: number;
  };
  errorRate: {
    warning: number;
    critical: number;
  };
}

/**
 * 性能监控器
 * 
 * 职责：
 * 1. 收集性能指标
 * 2. 分析性能趋势
 * 3. 生成性能报告
 * 4. 提供性能告警
 * 5. 建议性能优化
 */
export class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private maxMetrics: number;
  private thresholds: PerformanceThresholds;
  private startTime: number;

  constructor(
    maxMetrics: number = 1000,
    thresholds: Partial<PerformanceThresholds> = {}
  ) {
    this.maxMetrics = maxMetrics;
    this.startTime = Date.now();
    
    this.thresholds = {
      responseTime: {
        warning: 1000, // 1秒
        critical: 3000, // 3秒
      },
      memoryUsage: {
        warning: 100 * 1024 * 1024, // 100MB
        critical: 200 * 1024 * 1024, // 200MB
      },
      cacheHitRate: {
        warning: 0.7, // 70%
        critical: 0.5, // 50%
      },
      errorRate: {
        warning: 0.05, // 5%
        critical: 0.1, // 10%
      },
      ...thresholds,
    };
  }

  /**
   * 记录性能指标
   */
  public recordMetric(
    name: string,
    value: number,
    unit: string,
    category: PerformanceMetric['category'],
    tags?: Record<string, string>
  ): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      category,
      timestamp: Date.now(),
      tags,
    };

    this.metrics.push(metric);

    // 保持指标数量在限制内
    if (this.metrics.length > this.maxMetrics) {
      this.metrics.shift();
    }
  }

  /**
   * 记录响应时间
   */
  public recordResponseTime(operation: string, duration: number, tags?: Record<string, string>): void {
    this.recordMetric(
      `response_time.${operation}`,
      duration,
      'ms',
      'response_time',
      tags
    );
  }

  /**
   * 记录内存使用
   */
  public recordMemoryUsage(usage: number, tags?: Record<string, string>): void {
    this.recordMetric(
      'memory_usage',
      usage,
      'bytes',
      'memory',
      tags
    );
  }

  /**
   * 记录缓存命中率
   */
  public recordCacheHitRate(hitRate: number, tags?: Record<string, string>): void {
    this.recordMetric(
      'cache_hit_rate',
      hitRate,
      'ratio',
      'cache',
      tags
    );
  }

  /**
   * 记录吞吐量
   */
  public recordThroughput(operations: number, timeWindow: number, tags?: Record<string, string>): void {
    const throughput = operations / (timeWindow / 1000); // ops/sec
    this.recordMetric(
      'throughput',
      throughput,
      'ops/sec',
      'throughput',
      tags
    );
  }

  /**
   * 记录错误
   */
  public recordError(errorType: string, tags?: Record<string, string>): void {
    this.recordMetric(
      `error.${errorType}`,
      1,
      'count',
      'error',
      tags
    );
  }

  /**
   * 计算统计信息
   */
  private calculateStats(values: number[]): PerformanceStats {
    if (values.length === 0) {
      return {
        min: 0,
        max: 0,
        avg: 0,
        p50: 0,
        p90: 0,
        p95: 0,
        p99: 0,
        count: 0,
        sum: 0,
      };
    }

    const sorted = [...values].sort((a, b) => a - b);
    const sum = values.reduce((a, b) => a + b, 0);

    return {
      min: sorted[0],
      max: sorted[sorted.length - 1],
      avg: sum / values.length,
      p50: this.percentile(sorted, 0.5),
      p90: this.percentile(sorted, 0.9),
      p95: this.percentile(sorted, 0.95),
      p99: this.percentile(sorted, 0.99),
      count: values.length,
      sum,
    };
  }

  /**
   * 计算百分位数
   */
  private percentile(sortedValues: number[], p: number): number {
    const index = Math.ceil(sortedValues.length * p) - 1;
    return sortedValues[Math.max(0, index)];
  }

  /**
   * 获取指定时间范围内的指标
   */
  private getMetricsInRange(startTime: number, endTime: number): PerformanceMetric[] {
    return this.metrics.filter(
      metric => metric.timestamp >= startTime && metric.timestamp <= endTime
    );
  }

  /**
   * 生成性能报告
   */
  public generateReport(timeRangeMs: number = 5 * 60 * 1000): PerformanceReport {
    const endTime = Date.now();
    const startTime = endTime - timeRangeMs;
    const metricsInRange = this.getMetricsInRange(startTime, endTime);

    // 响应时间统计
    const responseTimeMetrics = metricsInRange
      .filter(m => m.category === 'response_time')
      .map(m => m.value);
    
    // 内存使用统计
    const memoryMetrics = metricsInRange
      .filter(m => m.category === 'memory')
      .map(m => m.value);

    // 缓存命中率
    const cacheMetrics = metricsInRange
      .filter(m => m.category === 'cache')
      .map(m => m.value);
    const cacheHitRate = cacheMetrics.length > 0 
      ? cacheMetrics.reduce((a, b) => a + b, 0) / cacheMetrics.length 
      : 0;

    // 吞吐量
    const throughputMetrics = metricsInRange
      .filter(m => m.category === 'throughput')
      .map(m => m.value);
    const throughput = throughputMetrics.length > 0
      ? throughputMetrics.reduce((a, b) => a + b, 0) / throughputMetrics.length
      : 0;

    // 错误率
    const errorCount = metricsInRange.filter(m => m.category === 'error').length;
    const totalOperations = responseTimeMetrics.length;
    const errorRate = totalOperations > 0 ? errorCount / totalOperations : 0;

    // 生成告警
    const alerts = this.generateAlerts({
      responseTime: this.calculateStats(responseTimeMetrics),
      memoryUsage: this.calculateStats(memoryMetrics),
      cacheHitRate,
      throughput,
      errorRate,
    });

    // 生成建议
    const recommendations = this.generateRecommendations({
      responseTime: this.calculateStats(responseTimeMetrics),
      memoryUsage: this.calculateStats(memoryMetrics),
      cacheHitRate,
      throughput,
      errorRate,
    });

    return {
      timeRange: {
        start: startTime,
        end: endTime,
        duration: timeRangeMs,
      },
      metrics: {
        responseTime: this.calculateStats(responseTimeMetrics),
        memoryUsage: this.calculateStats(memoryMetrics),
        cacheHitRate,
        throughput,
        errorRate,
      },
      alerts,
      recommendations,
    };
  }

  /**
   * 生成告警
   */
  private generateAlerts(metrics: any): PerformanceReport['alerts'] {
    const alerts: PerformanceReport['alerts'] = [];

    // 响应时间告警
    if (metrics.responseTime.avg > this.thresholds.responseTime.critical) {
      alerts.push({
        level: 'critical',
        message: 'Average response time is critically high',
        metric: 'response_time',
        value: metrics.responseTime.avg,
        threshold: this.thresholds.responseTime.critical,
      });
    } else if (metrics.responseTime.avg > this.thresholds.responseTime.warning) {
      alerts.push({
        level: 'warning',
        message: 'Average response time is high',
        metric: 'response_time',
        value: metrics.responseTime.avg,
        threshold: this.thresholds.responseTime.warning,
      });
    }

    // 内存使用告警
    if (metrics.memoryUsage.avg > this.thresholds.memoryUsage.critical) {
      alerts.push({
        level: 'critical',
        message: 'Memory usage is critically high',
        metric: 'memory_usage',
        value: metrics.memoryUsage.avg,
        threshold: this.thresholds.memoryUsage.critical,
      });
    } else if (metrics.memoryUsage.avg > this.thresholds.memoryUsage.warning) {
      alerts.push({
        level: 'warning',
        message: 'Memory usage is high',
        metric: 'memory_usage',
        value: metrics.memoryUsage.avg,
        threshold: this.thresholds.memoryUsage.warning,
      });
    }

    // 缓存命中率告警
    if (metrics.cacheHitRate < this.thresholds.cacheHitRate.critical) {
      alerts.push({
        level: 'critical',
        message: 'Cache hit rate is critically low',
        metric: 'cache_hit_rate',
        value: metrics.cacheHitRate,
        threshold: this.thresholds.cacheHitRate.critical,
      });
    } else if (metrics.cacheHitRate < this.thresholds.cacheHitRate.warning) {
      alerts.push({
        level: 'warning',
        message: 'Cache hit rate is low',
        metric: 'cache_hit_rate',
        value: metrics.cacheHitRate,
        threshold: this.thresholds.cacheHitRate.warning,
      });
    }

    // 错误率告警
    if (metrics.errorRate > this.thresholds.errorRate.critical) {
      alerts.push({
        level: 'critical',
        message: 'Error rate is critically high',
        metric: 'error_rate',
        value: metrics.errorRate,
        threshold: this.thresholds.errorRate.critical,
      });
    } else if (metrics.errorRate > this.thresholds.errorRate.warning) {
      alerts.push({
        level: 'warning',
        message: 'Error rate is high',
        metric: 'error_rate',
        value: metrics.errorRate,
        threshold: this.thresholds.errorRate.warning,
      });
    }

    return alerts;
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(metrics: any): string[] {
    const recommendations: string[] = [];

    if (metrics.responseTime.avg > 1000) {
      recommendations.push('Consider implementing caching for frequently accessed data');
      recommendations.push('Optimize database queries and API calls');
    }

    if (metrics.memoryUsage.avg > 50 * 1024 * 1024) {
      recommendations.push('Review memory usage patterns and implement garbage collection');
      recommendations.push('Consider using object pooling for frequently created objects');
    }

    if (metrics.cacheHitRate < 0.8) {
      recommendations.push('Review cache strategy and increase cache size if needed');
      recommendations.push('Implement cache warming for frequently accessed data');
    }

    if (metrics.errorRate > 0.01) {
      recommendations.push('Investigate and fix sources of errors');
      recommendations.push('Implement better error handling and retry mechanisms');
    }

    if (metrics.throughput < 10) {
      recommendations.push('Consider implementing batch processing for better throughput');
      recommendations.push('Review bottlenecks in the processing pipeline');
    }

    return recommendations;
  }

  /**
   * 清理旧指标
   */
  public cleanup(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge;
    this.metrics = this.metrics.filter(metric => metric.timestamp > cutoff);
  }

  /**
   * 获取实时统计
   */
  public getRealTimeStats(): {
    totalMetrics: number;
    metricsPerSecond: number;
    uptime: number;
    memoryUsage: number;
  } {
    const now = Date.now();
    const uptime = now - this.startTime;
    const metricsPerSecond = this.metrics.length / (uptime / 1000);

    return {
      totalMetrics: this.metrics.length,
      metricsPerSecond: Math.round(metricsPerSecond * 100) / 100,
      uptime,
      memoryUsage: this.estimateMemoryUsage(),
    };
  }

  /**
   * 估算内存使用
   */
  private estimateMemoryUsage(): number {
    // 简单估算：每个指标大约占用 200 字节
    return this.metrics.length * 200;
  }
}
