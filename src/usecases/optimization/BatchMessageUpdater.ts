import { Msg, MessageStatus } from '@/core/types/chat';

/**
 * 批量更新操作类型
 */
interface BatchUpdateOperation {
  messageId: string;
  updates: Partial<Msg>;
  priority: 'low' | 'normal' | 'high';
  timestamp: number;
}

/**
 * 批量更新配置
 */
interface BatchUpdateConfig {
  batchSize: number; // 批次大小
  flushInterval: number; // 刷新间隔（毫秒）
  maxWaitTime: number; // 最大等待时间（毫秒）
  priorityThreshold: number; // 高优先级操作的阈值
}

/**
 * 批量更新统计信息
 */
interface BatchUpdateStats {
  totalOperations: number;
  batchesProcessed: number;
  averageBatchSize: number;
  averageWaitTime: number;
  highPriorityOperations: number;
  lastFlushTime: number;
}

/**
 * 批量消息更新器
 * 
 * 职责：
 * 1. 收集消息更新操作
 * 2. 批量处理更新以提升性能
 * 3. 支持优先级处理
 * 4. 防抖和节流机制
 * 5. 提供统计和监控功能
 */
export class BatchMessageUpdater {
  private pendingOperations = new Map<string, BatchUpdateOperation>();
  private config: BatchUpdateConfig;
  private stats: BatchUpdateStats;
  private flushTimer: NodeJS.Timeout | null = null;
  private isProcessing = false;

  // 回调函数
  private onBatchUpdate?: (updates: Map<string, Partial<Msg>>) => Promise<void>;
  private onError?: (error: Error, operations: BatchUpdateOperation[]) => void;

  constructor(
    config: Partial<BatchUpdateConfig> = {},
    callbacks: {
      onBatchUpdate?: (updates: Map<string, Partial<Msg>>) => Promise<void>;
      onError?: (error: Error, operations: BatchUpdateOperation[]) => void;
    } = {}
  ) {
    this.config = {
      batchSize: 10,
      flushInterval: 100, // 100ms
      maxWaitTime: 1000, // 1秒
      priorityThreshold: 5,
      ...config,
    };

    this.stats = {
      totalOperations: 0,
      batchesProcessed: 0,
      averageBatchSize: 0,
      averageWaitTime: 0,
      highPriorityOperations: 0,
      lastFlushTime: 0,
    };

    this.onBatchUpdate = callbacks.onBatchUpdate;
    this.onError = callbacks.onError;
  }

  /**
   * 调度消息更新
   */
  public scheduleUpdate(
    messageId: string, 
    updates: Partial<Msg>, 
    priority: 'low' | 'normal' | 'high' = 'normal'
  ): void {
    const operation: BatchUpdateOperation = {
      messageId,
      updates,
      priority,
      timestamp: Date.now(),
    };

    // 合并已存在的操作
    const existingOperation = this.pendingOperations.get(messageId);
    if (existingOperation) {
      // 合并更新，保持最高优先级
      operation.updates = { ...existingOperation.updates, ...updates };
      operation.priority = this.getHigherPriority(existingOperation.priority, priority);
      operation.timestamp = existingOperation.timestamp; // 保持原始时间戳
    }

    this.pendingOperations.set(messageId, operation);
    this.stats.totalOperations++;

    if (priority === 'high') {
      this.stats.highPriorityOperations++;
    }

    // 检查是否需要立即刷新
    if (this.shouldFlushImmediately()) {
      this.flushImmediately();
    } else {
      this.scheduleFlush();
    }
  }

  /**
   * 立即刷新所有待处理的更新
   */
  public async flushImmediately(): Promise<void> {
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
      this.flushTimer = null;
    }

    await this.processPendingOperations();
  }

  /**
   * 调度刷新
   */
  private scheduleFlush(): void {
    if (this.flushTimer) {
      return; // 已经调度了
    }

    this.flushTimer = setTimeout(() => {
      this.flushTimer = null;
      this.processPendingOperations();
    }, this.config.flushInterval);
  }

  /**
   * 判断是否应该立即刷新
   */
  private shouldFlushImmediately(): boolean {
    // 批次大小达到阈值
    if (this.pendingOperations.size >= this.config.batchSize) {
      return true;
    }

    // 高优先级操作数量达到阈值
    const highPriorityCount = Array.from(this.pendingOperations.values())
      .filter(op => op.priority === 'high').length;
    
    if (highPriorityCount >= this.config.priorityThreshold) {
      return true;
    }

    // 最老的操作等待时间超过最大等待时间
    const oldestOperation = this.getOldestOperation();
    if (oldestOperation && Date.now() - oldestOperation.timestamp > this.config.maxWaitTime) {
      return true;
    }

    return false;
  }

  /**
   * 处理待处理的操作
   */
  private async processPendingOperations(): Promise<void> {
    if (this.isProcessing || this.pendingOperations.size === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      // 获取当前批次的操作
      const operations = Array.from(this.pendingOperations.values());
      const updates = new Map<string, Partial<Msg>>();

      // 按优先级排序
      operations.sort((a, b) => {
        const priorityOrder = { high: 3, normal: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });

      // 构建更新映射
      for (const operation of operations) {
        updates.set(operation.messageId, operation.updates);
      }

      // 清空待处理操作
      this.pendingOperations.clear();

      // 执行批量更新
      if (this.onBatchUpdate) {
        await this.onBatchUpdate(updates);
      }

      // 更新统计信息
      this.updateStats(operations);

    } catch (error) {
      console.error('Batch update failed:', error);
      
      if (this.onError) {
        const operations = Array.from(this.pendingOperations.values());
        this.onError(error as Error, operations);
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 获取最老的操作
   */
  private getOldestOperation(): BatchUpdateOperation | undefined {
    let oldest: BatchUpdateOperation | undefined;
    
    for (const operation of this.pendingOperations.values()) {
      if (!oldest || operation.timestamp < oldest.timestamp) {
        oldest = operation;
      }
    }
    
    return oldest;
  }

  /**
   * 获取更高的优先级
   */
  private getHigherPriority(
    priority1: 'low' | 'normal' | 'high', 
    priority2: 'low' | 'normal' | 'high'
  ): 'low' | 'normal' | 'high' {
    const priorityOrder = { low: 1, normal: 2, high: 3 };
    return priorityOrder[priority1] >= priorityOrder[priority2] ? priority1 : priority2;
  }

  /**
   * 更新统计信息
   */
  private updateStats(operations: BatchUpdateOperation[]): void {
    this.stats.batchesProcessed++;
    this.stats.lastFlushTime = Date.now();

    // 计算平均批次大小
    const totalBatchSize = this.stats.averageBatchSize * (this.stats.batchesProcessed - 1) + operations.length;
    this.stats.averageBatchSize = totalBatchSize / this.stats.batchesProcessed;

    // 计算平均等待时间
    const currentWaitTime = operations.length > 0 
      ? operations.reduce((sum, op) => sum + (Date.now() - op.timestamp), 0) / operations.length
      : 0;
    
    const totalWaitTime = this.stats.averageWaitTime * (this.stats.batchesProcessed - 1) + currentWaitTime;
    this.stats.averageWaitTime = totalWaitTime / this.stats.batchesProcessed;
  }

  /**
   * 获取统计信息
   */
  public getStats(): BatchUpdateStats & { pendingOperations: number } {
    return {
      ...this.stats,
      pendingOperations: this.pendingOperations.size,
    };
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      totalOperations: 0,
      batchesProcessed: 0,
      averageBatchSize: 0,
      averageWaitTime: 0,
      highPriorityOperations: 0,
      lastFlushTime: 0,
    };
  }

  /**
   * 获取配置信息
   */
  public getConfig(): BatchUpdateConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<BatchUpdateConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 销毁更新器
   */
  public destroy(): void {
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
      this.flushTimer = null;
    }

    // 处理剩余的操作
    if (this.pendingOperations.size > 0) {
      this.processPendingOperations();
    }
  }

  /**
   * 检查健康状态
   */
  public getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // 检查待处理操作数量
    if (this.pendingOperations.size > this.config.batchSize * 2) {
      issues.push(`Too many pending operations: ${this.pendingOperations.size}`);
      recommendations.push('Consider reducing batch size or flush interval');
    }

    // 检查平均等待时间
    if (this.stats.averageWaitTime > this.config.maxWaitTime) {
      issues.push(`Average wait time too high: ${this.stats.averageWaitTime}ms`);
      recommendations.push('Consider reducing max wait time or batch size');
    }

    // 检查是否有长时间未处理的操作
    const oldestOperation = this.getOldestOperation();
    if (oldestOperation && Date.now() - oldestOperation.timestamp > this.config.maxWaitTime * 2) {
      issues.push('Some operations have been waiting too long');
      recommendations.push('Force flush or check for processing bottlenecks');
    }

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (issues.length > 0) {
      status = issues.length > 2 ? 'critical' : 'warning';
    }

    return { status, issues, recommendations };
  }
}
