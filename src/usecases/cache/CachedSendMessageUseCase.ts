import { SendMessageUseCase, SendMessageInput, SendMessageOutput } from '../message/SendMessageUseCase';
import { MessageContent } from '@/core/types/chat';

/**
 * 缓存键生成器
 */
class CacheKeyGenerator {
  /**
   * 生成消息发送的缓存键
   */
  static generateMessageKey(input: SendMessageInput): string {
    const contentHash = this.hashContent(input.content);
    const contextHash = this.hashContext(input.chatId);
    
    return `msg:${contentHash}:${contextHash}`;
  }

  /**
   * 生成内容哈希
   */
  private static hashContent(content: MessageContent): string {
    const contentStr = typeof content === 'string' 
      ? content 
      : JSON.stringify(content);
    
    // 简单的哈希函数（生产环境应使用更强的哈希算法）
    let hash = 0;
    for (let i = 0; i < contentStr.length; i++) {
      const char = contentStr.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * 生成上下文哈希（基于聊天历史）
   */
  private static hashContext(chatId: string): string {
    // 简化实现：使用chatId作为上下文标识
    // 实际应用中可能需要考虑最近几条消息的内容
    return chatId.slice(-8); // 取chatId的后8位作为上下文标识
  }
}

/**
 * 缓存条目接口
 */
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // 生存时间（毫秒）
  accessCount: number;
  lastAccess: number;
}

/**
 * LRU缓存实现
 */
class LRUCache<K, V> {
  private cache = new Map<K, CacheEntry<V>>();
  private maxSize: number;
  private defaultTTL: number;

  constructor(maxSize: number = 100, defaultTTL: number = 5 * 60 * 1000) { // 默认5分钟TTL
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
  }

  /**
   * 获取缓存项
   */
  get(key: K): V | undefined {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return undefined;
    }

    // 检查是否过期
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return undefined;
    }

    // 更新访问信息
    entry.accessCount++;
    entry.lastAccess = Date.now();

    // 移到最后（LRU策略）
    this.cache.delete(key);
    this.cache.set(key, entry);

    return entry.data;
  }

  /**
   * 设置缓存项
   */
  set(key: K, value: V, ttl?: number): void {
    // 如果已存在，先删除
    if (this.cache.has(key)) {
      this.cache.delete(key);
    }

    // 如果超过最大大小，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      }
    }

    // 添加新项
    const entry: CacheEntry<V> = {
      data: value,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
      accessCount: 1,
      lastAccess: Date.now(),
    };

    this.cache.set(key, entry);
  }

  /**
   * 检查是否过期
   */
  private isExpired(entry: CacheEntry<V>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * 清理过期项
   */
  cleanup(): void {
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const entries = Array.from(this.cache.values());
    
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      totalAccess: entries.reduce((sum, entry) => sum + entry.accessCount, 0),
      averageAge: entries.length > 0 
        ? entries.reduce((sum, entry) => sum + (Date.now() - entry.timestamp), 0) / entries.length
        : 0,
    };
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
  }
}

/**
 * 带缓存的消息发送用例
 * 
 * 职责：
 * 1. 为消息发送提供智能缓存
 * 2. 避免重复的API调用
 * 3. 提升响应速度
 * 4. 管理缓存生命周期
 */
export class CachedSendMessageUseCase extends SendMessageUseCase {
  private cache = new LRUCache<string, SendMessageOutput>(50, 10 * 60 * 1000); // 10分钟TTL
  private cacheHits = 0;
  private cacheMisses = 0;

  /**
   * 执行消息发送（带缓存）
   */
  async execute(input: SendMessageInput): Promise<SendMessageOutput> {
    // 生成缓存键
    const cacheKey = CacheKeyGenerator.generateMessageKey(input);

    // 检查缓存
    const cachedResult = this.cache.get(cacheKey);
    if (cachedResult && this.shouldUseCache(input)) {
      this.cacheHits++;
      console.log(`Cache hit for message: ${cacheKey}`);
      
      // 返回缓存结果的副本，避免修改原始数据
      return this.cloneCachedResult(cachedResult);
    }

    // 缓存未命中，执行实际逻辑
    this.cacheMisses++;
    console.log(`Cache miss for message: ${cacheKey}`);
    
    const result = await super.execute(input);

    // 如果结果可以缓存，则缓存它
    if (this.shouldCacheResult(result)) {
      this.cache.set(cacheKey, result);
    }

    return result;
  }

  /**
   * 判断是否应该使用缓存
   */
  private shouldUseCache(input: SendMessageInput): boolean {
    // 重发消息不使用缓存
    if (input.resend) {
      return false;
    }

    // 包含图片的消息不使用缓存
    if (this.hasImages(input.content)) {
      return false;
    }

    return true;
  }

  /**
   * 判断结果是否应该缓存
   */
  private shouldCacheResult(result: SendMessageOutput): boolean {
    // 只缓存成功的结果
    return result.success;
  }

  /**
   * 检查内容是否包含图片
   */
  private hasImages(content: MessageContent): boolean {
    if (Array.isArray(content)) {
      return content.some(item => typeof item === 'object' && item.type === 'image');
    }
    
    return typeof content === 'object' && content.type === 'image';
  }

  /**
   * 克隆缓存结果
   */
  private cloneCachedResult(result: SendMessageOutput): SendMessageOutput {
    return {
      ...result,
      userMessage: result.userMessage ? { ...result.userMessage } : result.userMessage,
      assistantMessage: { ...result.assistantMessage },
    };
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    const cacheStats = this.cache.getStats();
    const hitRate = this.cacheHits + this.cacheMisses > 0 
      ? this.cacheHits / (this.cacheHits + this.cacheMisses) 
      : 0;

    return {
      ...cacheStats,
      hits: this.cacheHits,
      misses: this.cacheMisses,
      hitRate: Math.round(hitRate * 100) / 100,
    };
  }

  /**
   * 清理过期缓存
   */
  cleanupCache(): void {
    this.cache.cleanup();
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.cacheHits = 0;
    this.cacheMisses = 0;
  }

  /**
   * 预热缓存（可选功能）
   */
  async warmupCache(commonInputs: SendMessageInput[]): Promise<void> {
    console.log(`Warming up cache with ${commonInputs.length} common inputs...`);
    
    for (const input of commonInputs) {
      try {
        await this.execute(input);
      } catch (error) {
        console.warn(`Failed to warm up cache for input:`, error);
      }
    }
  }
}
