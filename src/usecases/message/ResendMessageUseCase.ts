import { UseCase } from '@/usecases/base/UseCase';
import { IMessageRepository } from '@/infrastructure/MessageRepository';
import { SendMessageUseCase } from './SendMessageUseCase';
import { Msg, MessageContent } from '@/core/types/chat';

export interface ResendMessageInput {
  chatId: string;
  assistantMessageId: string;
  // 回调函数，用于实时更新 UI
  onMessageUpdate?: (message: Msg) => void;
  onUserMessageCreated?: (message: Msg) => void;
  onAssistantMessageCreated?: (message: Msg) => void;
  onMessagesDeleted?: (deletedMessageIds: string[]) => void;
}

export interface ResendMessageOutput {
  userMessage?: Msg;
  assistantMessage: Msg;
  deletedMessageIds: string[];
  success: boolean;
}

export class ResendMessageUseCase extends UseCase<ResendMessageInput, ResendMessageOutput> {
  constructor(
    private messageRepo: IMessageRepository,
    private sendMessageUseCase: SendMessageUseCase
  ) {
    super();
  }

  async execute(input: ResendMessageInput): Promise<ResendMessageOutput> {
    this.validateInput(input);

    // 1. 获取聊天中的所有消息
    const allMessages = await this.messageRepo.findByChatId(input.chatId);
    
    // 2. 找到要重新发送的助手消息的索引
    const assistantMsgIndex = allMessages.findIndex(msg => msg.id === input.assistantMessageId);
    
    if (assistantMsgIndex === -1) {
      throw new Error('Assistant message not found');
    }

    // 3. 找到对应的用户消息内容
    const userMessageContent = await this.findUserMessageContent(allMessages, assistantMsgIndex);
    
    // 4. 删除助手消息及其后面的所有消息
    const deletedMessageIds = await this.deleteMessagesFromIndex(allMessages, assistantMsgIndex);
    
    // 通知 UI 层消息已删除
    if (input.onMessagesDeleted) {
      input.onMessagesDeleted(deletedMessageIds);
    }

    // 5. 重新执行发送消息流程，但不创建用户消息
    const result = await this.sendMessageUseCase.execute({
      chatId: input.chatId,
      content: userMessageContent,
      skipUserMessage: true, // 跳过用户消息创建
      onMessageUpdate: input.onMessageUpdate,
      onUserMessageCreated: input.onUserMessageCreated, // 这个不会被调用
      onAssistantMessageCreated: input.onAssistantMessageCreated,
    });

    return {
      userMessage: undefined, // 重发时不创建新的用户消息
      assistantMessage: result.assistantMessage,
      deletedMessageIds,
      success: true,
    };
  }

  /**
   * 找到助手消息对应的用户消息内容
   */
  private async findUserMessageContent(allMessages: Msg[], assistantMsgIndex: number): Promise<MessageContent> {
    // 从助手消息往前找最近的用户消息
    for (let i = assistantMsgIndex - 1; i >= 0; i--) {
      if (allMessages[i].role === 'user') {
        return allMessages[i].content;
      }
    }
    
    throw new Error('No user message found before the assistant message');
  }

  /**
   * 删除从指定索引开始的所有消息
   */
  private async deleteMessagesFromIndex(allMessages: Msg[], startIndex: number): Promise<string[]> {
    const messagesToDelete = allMessages.slice(startIndex);
    const deletedIds: string[] = [];

    for (const message of messagesToDelete) {
      try {
        await this.deleteMessage(message.id);
        deletedIds.push(message.id);
      } catch (error) {
        console.error(`Failed to delete message ${message.id}:`, error);
        // 继续删除其他消息，不要因为一个失败就停止
      }
    }

    return deletedIds;
  }

  /**
   * 删除单个消息
   */
  private async deleteMessage(messageId: string): Promise<void> {
    await this.messageRepo.delete(messageId);
  }

  private validateInput(input: ResendMessageInput) {
    if (!input.chatId) {
      throw new Error('Chat ID is required');
    }
    
    if (!input.assistantMessageId) {
      throw new Error('Assistant message ID is required');
    }
  }
}
