
import { UseCase } from '@/usecases/base/UseCase';
import { IMessageRepository, UpdateMessageParams } from '@/infrastructure/MessageRepository';
import { MessageStatus, MessageContent } from '@/core/types/chat';

export interface UpdateMessageInput {
  messageId: string;
  content?: MessageContent;
  thinkContent?: string;
  status?: MessageStatus;
}

export interface UpdateMessageOutput {
  success: boolean;
}

export class UpdateMessageUseCase extends UseCase<UpdateMessageInput, UpdateMessageOutput> {
  constructor(private messageRepo: IMessageRepository) {
    super();
  }

  async execute(input: UpdateMessageInput): Promise<UpdateMessageOutput> {
    this.validateInput(input);

    const updateParams: UpdateMessageParams = {
      messageId: input.messageId,
      content: input.content,
      thinkContent: input.thinkContent,
      status: input.status,
    };

    await this.messageRepo.update(updateParams);

    return {
      success: true,
    };
  }

  private validateInput(input: UpdateMessageInput) {
    if (!input.messageId) {
      throw new Error('Message ID is required');
    }
  }
}
