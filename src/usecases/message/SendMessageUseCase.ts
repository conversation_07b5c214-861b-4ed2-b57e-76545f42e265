import { UseCase } from '@/usecases/base/UseCase';
import { IMessageRepository, CreateMessageParams } from '@/infrastructure/MessageRepository';
import { IAIProvider } from '@/infrastructure/AIProvider';
import { IChatRepository } from '@/infrastructure/ChatRepository';
import { ProcessThinkingUseCase } from './ProcessThinkingUseCase';
import { UpdateMessageUseCase } from './UpdateMessageUseCase';
import { Msg, MessageContent, Chat, MessageStatus } from '@/core/types/chat';
import { v4 as uuid } from 'uuid';

export interface SendMessageInput {
  chatId: string;
  content: MessageContent;
  resend?: boolean;
  assistantMessageId?: string;
  skipUserMessage?: boolean; // 新增：跳过用户消息创建，只创建助手消息
  // 回调函数，用于实时更新 UI
  onMessageUpdate?: (message: Msg) => void;
  onUserMessageCreated?: (message: Msg) => void;
  onAssistantMessageCreated?: (message: Msg) => void;
}

export interface SendMessageOutput {
  userMessage?: Msg;
  assistantMessage: Msg;
  success: boolean;
}

export class SendMessageUseCase extends UseCase<SendMessageInput, SendMessageOutput> {
  constructor(
    private messageRepo: IMessageRepository,
    private aiProvider: IAIProvider,
    private chatRepo: IChatRepository,
    private processThinking: ProcessThinkingUseCase,
    private updateMessage: UpdateMessageUseCase
  ) {
    super();
  }

  async execute(input: SendMessageInput): Promise<SendMessageOutput> {
    this.validateInput(input);

    const chat = await this.ensureChatExists(input.chatId);

    let userMessage: Msg | undefined;
    let actualContent = input.content;

    if (!input.skipUserMessage && !input.resend) {
      // 正常发送：创建用户消息
      userMessage = await this.messageRepo.create({
        chatId: chat.id,
        content: input.content,
        role: 'user',
      });

      // 通知 UI 层用户消息已创建
      if (input.onUserMessageCreated) {
        input.onUserMessageCreated(userMessage);
      }
    } else if (input.resend && !input.skipUserMessage) {
      // 传统重发模式：需要找到对应的用户消息内容
      actualContent = await this.getOriginalUserMessageContent(input.chatId, input.assistantMessageId);
    }
    // 如果 skipUserMessage=true，则跳过用户消息创建，直接使用传入的 content

    const assistantMessage = await this.createOrUpdateAssistantMessage(input);

    // 通知 UI 层助手消息已创建
    if (input.onAssistantMessageCreated) {
      input.onAssistantMessageCreated(assistantMessage);
    }

    await this.handleAIResponse(chat.id, assistantMessage.id, { ...input, content: actualContent });

    return {
      userMessage,
      assistantMessage,
      success: true,
    };
  }

  private validateInput(input: SendMessageInput) {
    if (!input.chatId || input.chatId === '0') {
      throw new Error('No active session, cannot send message');
    }
  }

  private async ensureChatExists(chatId: string): Promise<Chat> {
    let chat = await this.chatRepo.findById(chatId);
    if (!chat) {
        // This part of the logic for creating a new chat needs to be clarified.
        // For now, we'll assume the chat exists.
        // In a real scenario, you might create a new chat here.
        throw new Error(`Chat with id ${chatId} not found.`);
    }
    return chat;
  }

  private async createOrUpdateAssistantMessage(input: SendMessageInput): Promise<Msg> {
    if (input.assistantMessageId) {
        const existingMsg = await this.messageRepo.findById(input.assistantMessageId);
        if (existingMsg) {
          // 重新发送时，重置助手消息的状态和内容
          if (input.resend) {
            await this.updateMessage.execute({
              messageId: existingMsg.id,
              content: '',
              thinkContent: '',
              status: MessageStatus.WAITING,
            });
            // 返回更新后的消息
            const updatedMsg = await this.messageRepo.findById(existingMsg.id);
            return updatedMsg || existingMsg;
          }
          return existingMsg;
        }
    }

    const assistantMessageParams: CreateMessageParams = {
        chatId: input.chatId,
        content: '',
        role: 'assistant',
        status: MessageStatus.WAITING, // 设置初始状态为WAITING，确保显示等待指示器
    };

    console.log('[SendMessageUseCase] Creating assistant message with WAITING status:', assistantMessageParams);
    return this.messageRepo.create(assistantMessageParams);
  }

  /**
   * 获取原始用户消息内容（用于重新发送）
   */
  private async getOriginalUserMessageContent(chatId: string, assistantMessageId?: string): Promise<MessageContent> {
    if (!assistantMessageId) {
      throw new Error('Assistant message ID is required for resend operation');
    }

    // 获取聊天中的所有消息
    const messages = await this.messageRepo.findByChatId(chatId);

    // 找到助手消息的索引
    const assistantMsgIndex = messages.findIndex(msg => msg.id === assistantMessageId);

    if (assistantMsgIndex === -1) {
      throw new Error('Assistant message not found');
    }

    // 找到助手消息之前的最后一条用户消息
    for (let i = assistantMsgIndex - 1; i >= 0; i--) {
      if (messages[i].role === 'user') {
        return messages[i].content;
      }
    }

    throw new Error('No user message found before the assistant message');
  }

  private async handleAIResponse(chatId: string, messageId: string, input: SendMessageInput & { content: MessageContent }) {
    try {
      const stream = await this.aiProvider.sendMessage({
        chatId,
        content: input.content, // 这里的 content 已经是正确的内容（包括重发时获取的原始用户消息）
        assistantMessageId: messageId,
        maxMessages: 10,
        resend: input.resend,
      });

      let fullContent = '';
      for await (const chunk of stream) {
        fullContent += chunk;

        // 处理思维链和更新消息
        const thinkingResult = await this.processThinking.execute({
          content: fullContent,
          messageId
        });

        await this.updateMessage.execute({
          messageId,
          content: thinkingResult.mainContent,
          thinkContent: thinkingResult.thinkContent,
          status: thinkingResult.status,
        });

        // 通知 UI 层消息已更新
        if (input.onMessageUpdate) {
          const updatedMessage = await this.messageRepo.findById(messageId);
          if (updatedMessage) {
            input.onMessageUpdate(updatedMessage);
          }
        }
      }

      // 流式处理完成后，设置最终状态
      await this.updateMessage.execute({
        messageId,
        status: MessageStatus.COMPLETED,
      });

      // 通知 UI 层消息最终完成
      if (input.onMessageUpdate) {
        const finalMessage = await this.messageRepo.findById(messageId);
        if (finalMessage) {
          input.onMessageUpdate(finalMessage);
        }
      }

    } catch (error) {
      console.error('AI response handling error:', error);

      // 设置错误状态
      await this.updateMessage.execute({
        messageId,
        status: MessageStatus.ERROR,
      });

      // 通知 UI 层消息出错
      if (input.onMessageUpdate) {
        const errorMessage = await this.messageRepo.findById(messageId);
        if (errorMessage) {
          input.onMessageUpdate(errorMessage);
        }
      }

      throw error;
    }
  }
}