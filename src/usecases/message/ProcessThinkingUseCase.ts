import { UseCase } from '@/usecases/base/UseCase';
import { MessageStatus } from '@/core/types/chat';
import { processContent, hasThinkingContent, isThinkingCompleted } from '@/core/utils/messageUtils';

export interface ProcessThinkingInput {
  content: string;
  messageId: string; // messageId might be needed for more complex logic in the future
}

export interface ProcessThinkingOutput {
  mainContent: string;
  thinkContent: string | undefined;
  status: MessageStatus;
}

export class ProcessThinkingUseCase extends UseCase<ProcessThinkingInput, ProcessThinkingOutput> {
  execute(input: ProcessThinkingInput): ProcessThinkingOutput {
    const { content } = input;

    const { mainContent, thinkContent } = processContent(content);

    let status: MessageStatus;

    if (hasThinkingContent(content) && !isThinkingCompleted(content)) {
      status = MessageStatus.THINKING;
    } else if (mainContent) {
      status = MessageStatus.GENERATING;
    } else {
      status = MessageStatus.WAITING;
    }

    // Note: The final COMPLETED status will be set by another mechanism,
    // likely after the entire stream is finished.

    return {
      mainContent,
      thinkContent,
      status,
    };
  }
}