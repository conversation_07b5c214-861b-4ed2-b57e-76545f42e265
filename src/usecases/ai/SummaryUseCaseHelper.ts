import { diContainer } from "@/usecases/container/DIContainer";
import { useModelStore } from "@/store/useModelStore";
import { useChatStore } from "@/store/useChatStore";
import type { GenerateSummaryInput, SummaryOutput } from "./SummaryUseCase";

/**
 * 摘要生成助手函数
 * 
 * 这个助手函数简化了 GenerateSummaryUseCase 的使用，
 * 自动处理 store 依赖的注入
 */
export async function generateSummary(originText: string): Promise<SummaryOutput> {
    const modelStore = useModelStore();
    const chatStore = useChatStore();
    
    const input: GenerateSummaryInput = {
        originText,
        enableSeparateSummaryModel: chatStore.enableSeparateSummaryModel,
        summaryModel: chatStore.summaryModel,
        summaryProvider: chatStore.summaryProvider,
        isModelConfigured: () => modelStore.isModelConfigured(),
        createOpenAIInstance: (provider?: string) => modelStore.createOpenAIInstance(provider),
        defaultModel: modelStore.model
    };
    
    return await diContainer.generateSummaryUseCase.execute(input);
}

/**
 * 检查是否可以生成摘要
 */
export function canGenerateSummary(): boolean {
    const modelStore = useModelStore();
    return modelStore.isModelConfigured().isValid;
}
