import { v4 as uuid } from "uuid";
import type { Msg, MessageContent } from "@/core/types/chat.ts";
import { MessageStatus } from "@/core/types/chat.ts";
import { messageDb } from "../persistence/message.db";
import { chatDb } from "../persistence/chat.db";
import { processContent } from "@/core/utils/messageUtils";

/**
 * 消息服务 - 处理消息的添加、更新和管理
 */
export class MessageService {
    /**
     * 添加新消息
     * @returns The created message object.
     */
    async addMessage(
        chatId: string,
        content: MessageContent,
        role: "user" | "assistant",
        options: Partial<Omit<Msg, "id" | "chatId" | "content" | "role">> = {}
    ): Promise<Msg> {
        if (!chatId || chatId === "0") {
            throw new Error("没有激活会话，不能发送消息");
        }

        const id = uuid();

        // 处理思考内容分离
        let mainContent = content;
        let thinkContent: string | undefined;

        if (typeof content === "string") {
            const processed = processContent(content);
            mainContent = processed.mainContent;
            thinkContent = processed.thinkContent || undefined;
        }

        const previewText = this.generatePreviewText(mainContent);

        const msg: Msg = {
            id,
            content: mainContent, // 只存储主要内容
            role,
            short:
                options.short ||
                previewText.slice(0, 50) +
                    (previewText.length > 50 ? "..." : ""),
            status: options.status ?? MessageStatus.COMPLETED,
            model: options.model || "",
            timestamp: options.timestamp || Date.now(),
            thinkContent, // 单独存储思考内容
            ...options,
        };

        await messageDb.createMessageFromObject(chatId, msg);

        // 更新聊天元数据
        const shortPreview =
            previewText.slice(0, 50) + (previewText.length > 50 ? "..." : "");
        await chatDb.updateMessageStats(
            chatId,
            await this.getChatMessageCount(chatId),
            shortPreview
        );

        return msg;
    }

    /**
     * 获取消息的预览文本
     * 将此逻辑从 Store 层移到 Service 层
     */
    generatePreviewText(content: MessageContent): string {
        let previewText = "[消息]";
        if (typeof content === "string") {
            previewText = content;
        } else if (
            Array.isArray(content) ||
            (content && typeof content === "object" && content.type === "image")
        ) {
            previewText = "[图片消息]";
        }
        return previewText;
    }

    /**
     * 更新消息
     * @returns The updated message object.
     */
    async updateMsg(
        id: string,
        updates: Partial<Omit<Msg, "id" | "chatId">>
    ): Promise<Msg> {
        const message = await messageDb.getMessage(id);
        if (!message) {
            throw new Error(`Message with id ${id} not found.`);
        }

        // 处理思考内容分离
        let processedUpdates = { ...updates };
        if (updates.content && typeof updates.content === "string") {
            const processed = processContent(updates.content);
            processedUpdates.content = processed.mainContent;
            // 只有在没有显式传递 thinkContent 时才使用处理结果中的 thinkContent
            if (updates.thinkContent === undefined) {
                processedUpdates.thinkContent = processed.thinkContent || undefined;
            }
        }

        // 更新数据库
        await messageDb.updateMessage(id, processedUpdates);

        // 如果内容更新了，可能需要更新聊天的预览
        if (processedUpdates.content !== undefined) {
            const previewText = this.generatePreviewText(processedUpdates.content);
            const messages = await messageDb.getChatMessages(message.chatId);
            if (
                messages.length > 0 &&
                messages[messages.length - 1].id === id
            ) {
                const shortPreview =
                    previewText.slice(0, 50) +
                    (previewText.length > 50 ? "..." : "");
                await chatDb.updateMessageStats(
                    message.chatId,
                    messages.length,
                    shortPreview
                );
            }
        }

        const updatedMessage = await this.getMessage(id);
        if (!updatedMessage) {
            throw new Error(
                `Failed to retrieve updated message with id ${id}.`
            );
        }
        return updatedMessage;
    }

    /**
     * 获取聊天的消息数量
     */
    async getChatMessageCount(chatId: string): Promise<number> {
        const messages = await messageDb.getChatMessages(chatId);
        return messages.length;
    }

    /**
     * 获取单个消息
     */
    async getMessage(id: string): Promise<Msg | undefined> {
        try {
            const message = await messageDb.getMessage(id);
            if (!message) return undefined;

            return {
                id: message.id,
                content: message.content,
                short: message.short,
                role: message.role,
                model: message.model,
                status: message.status,
                timestamp: message.timestamp,
                thinkContent: message.thinkContent,
            };
        } catch (error) {
            console.error("获取消息失败:", error);
            return undefined;
        }
    }

    /**
     * 获取聊天的所有消息
     */
    async getChatMessages(chatId: string): Promise<Msg[]> {
        if (chatId === "0") return [];

        try {
            const messages = await messageDb.getChatMessages(chatId);
            return messages.map((record) => ({
                id: record.id,
                content: record.content,
                short: record.short,
                role: record.role,
                model: record.model,
                status: record.status,
                timestamp: record.timestamp,
                thinkContent: record.thinkContent,
            }));
        } catch (error) {
            console.error("加载消息失败:", error);
            return [];
        }
    }
}

// 导出单例实例
export const messageService = new MessageService();
