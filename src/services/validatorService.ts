import type { ProviderConfig } from "@/core/types/model.ts";

class ValidatorService {
    /**
     * 校验指定 provider 的 API 配置是否有效
     * @param provider 要校验的 provider 配置
     */
    isApiConfigured(provider?: ProviderConfig): boolean {
        if (!provider) return false;
        return !!provider.baseUrl;
    }

    /**
     * 校验当前模型配置是否有效，返回详细原因
     * @param provider 当前选择的 provider 配置
     * @param currentModel 当前选择的模型
     * @param currentProviderId 当前选择的 provider ID
     */
    isModelConfigured(
        provider: ProviderConfig | undefined,
        currentModel: string,
        currentProviderId: string
    ) {
        if (!currentProviderId) {
            return {
                isValid: false,
                reason: "未选择默认模型供应商。",
                reasonKey: "NO_DEFAULT_PROVIDER",
            };
        }

        if (!provider) {
            return {
                isValid: false,
                reason: `找不到当前选择的供应商 ${currentProviderId} 配置。`,
                reasonKey: "PROVIDER_NOT_FOUND",
            };
        }

        if (!provider.isActive) {
            return {
                isValid: false,
                reason: "当前选择的供应商未激活。",
                reasonKey: "PROVIDER_NOT_ACTIVE",
            };
        }

        if (!provider.baseUrl) {
            return {
                isValid: false,
                reason: "当前供应商缺少基础 URL。",
                reasonKey: "BASE_URL_MISSING",
            };
        }

        if (!provider.apiKey) {
            return {
                isValid: false,
                reason: "当前供应商缺少 API 密钥。",
                reasonKey: "API_KEY_MISSING",
            };
        }

        const modelsForCurrentProvider = provider.selectedModels || [];

        if (currentModel && !modelsForCurrentProvider.includes(currentModel)) {
            return {
                isValid: false,
                reason: "当前选择的默认模型不在其激活供应商的模型列表中，请切换模型或检查供应商设置。",
                reasonKey: "DEFAULT_MODEL_NOT_IN_ACTIVE_PROVIDER_MODELS",
            };
        }

        if (!currentModel && modelsForCurrentProvider.length > 0) {
            return {
                isValid: false,
                reason: "已配置模型列表，但未选择默认模型。",
                reasonKey: "NO_DEFAULT_MODEL_SELECTED_WITH_AVAILABLE_MODELS",
            };
        }

        if (!currentModel && modelsForCurrentProvider.length === 0) {
            return {
                isValid: false,
                reason: "当前供应商没有可用的模型，请检查API配置或尝试更新模型列表。",
                reasonKey: "NO_MODELS_AVAILABLE_FOR_PROVIDER",
            };
        }

        return { isValid: true };
    }

    /**
     * 校验是否存在有效的 provider
     * @param activeProviders 激活的 provider 列表
     */
    hasValidProvider(activeProviders: ProviderConfig[]): boolean {
        if (activeProviders.length === 0) {
            return false;
        }
        return activeProviders.some((p) => !!p.baseUrl);
    }
}

export const validatorService = new ValidatorService();
