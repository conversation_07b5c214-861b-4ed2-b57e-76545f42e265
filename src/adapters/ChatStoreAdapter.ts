import { diContainer } from '@/usecases/container/DIContainer';
import { MessageContent, MessageStatus, Chat, Msg } from '@/core/types/chat';
import { chatService } from '@/services/chatService';
import { messageService } from '@/services/messageService';

/**
 * ChatStore 适配器 - 提供从现有 Store 到新用例架构的过渡
 * 这个适配器允许我们逐步迁移现有的 Store 逻辑到新的用例架构
 *
 * 这个适配器的职责：
 * 1. 调用用例层的业务逻辑
 * 2. 处理用例层和 UI 层之间的数据转换
 * 3. 提供向后兼容的接口
 */
export class ChatStoreAdapter {
  /**
   * 发送消息 - 使用新的 SendMessageUseCase
   * 这个方法处理完整的发送消息流程，包括：
   * 1. 创建用户消息（如果不是重发）
   * 2. 创建AI助手占位消息
   * 3. 调用AI并处理流式响应
   * 4. 实时更新消息状态和内容
   */
  async sendMessage(
    chatId: string,
    content: MessageContent,
    options: {
      resend?: boolean;
      assistantMessageId?: string;
      onMessageUpdate?: (message: Msg) => void; // 消息更新回调
      onUserMessageCreated?: (message: Msg) => void; // 用户消息创建回调
      onAssistantMessageCreated?: (message: Msg) => void; // AI消息创建回调
    } = {}
  ) {
    try {
      const result = await diContainer.sendMessageUseCase.execute({
        chatId,
        content,
        resend: options.resend,
        assistantMessageId: options.assistantMessageId,
        onMessageUpdate: options.onMessageUpdate,
        onUserMessageCreated: options.onUserMessageCreated,
        onAssistantMessageCreated: options.onAssistantMessageCreated,
      });

      return result;
    } catch (error) {
      console.error('ChatStoreAdapter: Error sending message:', error);
      throw error;
    }
  }

  /**
   * 创建新聊天 - 使用新的 CreateChatUseCase
   */
  async createChat(title?: string): Promise<Chat> {
    try {
      const result = await diContainer.createChatUseCase.execute({
        title,
      });

      return result.chat;
    } catch (error) {
      console.error('ChatStoreAdapter: Error creating chat:', error);
      throw error;
    }
  }

  /**
   * 加载聊天 - 使用新的 LoadChatUseCase
   */
  async loadChat(chatId: string): Promise<{ chat: Chat; messages: Msg[] }> {
    try {
      const result = await diContainer.loadChatUseCase.execute({
        chatId,
      });

      return {
        chat: result.chat,
        messages: result.messages,
      };
    } catch (error) {
      console.error('ChatStoreAdapter: Error loading chat:', error);
      throw error;
    }
  }

  /**
   * 更新消息 - 使用新的 UpdateMessageUseCase
   */
  async updateMessage(
    messageId: string,
    updates: {
      content?: MessageContent;
      thinkContent?: string;
      status?: MessageStatus;
    }
  ) {
    try {
      await diContainer.updateMessageUseCase.execute({
        messageId,
        ...updates,
      });
    } catch (error) {
      console.error('ChatStoreAdapter: Error updating message:', error);
      throw error;
    }
  }

  /**
   * 处理思维链 - 使用新的 ProcessThinkingUseCase
   */
  async processThinking(content: string, messageId: string) {
    try {
      const result = await diContainer.processThinkingUseCase.execute({
        content,
        messageId,
      });

      return result;
    } catch (error) {
      console.error('ChatStoreAdapter: Error processing thinking:', error);
      throw error;
    }
  }

  /**
   * 加载所有聊天 - 使用新的 LoadAllChatsUseCase
   */
  async loadAllChats(): Promise<Chat[]> {
    try {
      const result = await diContainer.loadAllChatsUseCase.execute({});
      return result.chats;
    } catch (error) {
      console.error('ChatStoreAdapter: Error loading all chats:', error);
      throw error;
    }
  }

  /**
   * 删除聊天 - 使用新的 RemoveChatUseCase
   */
  async removeChat(chatId: string): Promise<void> {
    try {
      await diContainer.removeChatUseCase.execute({
        chatId,
      });
    } catch (error) {
      console.error('ChatStoreAdapter: Error removing chat:', error);
      throw error;
    }
  }

  /**
   * 更新聊天 - 使用新的 UpdateChatUseCase
   */
  async updateChat(chatId: string, updates: Partial<Chat>): Promise<void> {
    try {
      await diContainer.updateChatUseCase.execute({
        chatId,
        updates,
      });
    } catch (error) {
      console.error('ChatStoreAdapter: Error updating chat:', error);
      throw error;
    }
  }

  /**
   * 重新发送消息 - 使用新的 ResendMessageUseCase
   */
  async resendMessage(
    chatId: string,
    assistantMessageId: string,
    options: {
      onMessageUpdate?: (message: Msg) => void;
      onUserMessageCreated?: (message: Msg) => void;
      onAssistantMessageCreated?: (message: Msg) => void;
      onMessagesDeleted?: (deletedMessageIds: string[]) => void;
    } = {}
  ) {
    try {
      const result = await diContainer.resendMessageUseCase.execute({
        chatId,
        assistantMessageId,
        onMessageUpdate: options.onMessageUpdate,
        onUserMessageCreated: options.onUserMessageCreated,
        onAssistantMessageCreated: options.onAssistantMessageCreated,
        onMessagesDeleted: options.onMessagesDeleted,
      });

      return result;
    } catch (error) {
      console.error('ChatStoreAdapter: Error resending message:', error);
      throw error;
    }
  }

  /**
   * 导出聊天历史 - 获取所有聊天和消息数据
   * 这个方法用于数据导出功能
   */
  async exportChatHistory(): Promise<Array<Chat & { messages: Msg[] }>> {
    try {
      // 获取所有聊天
      const chats = await this.loadAllChats();

      // 为每个聊天获取消息
      const chatsWithMessages = await Promise.all(
        chats.map(async (chat) => {
          const messages = await messageService.getChatMessages(chat.id);
          return {
            ...chat,
            messages: messages
          };
        })
      );

      return chatsWithMessages;
    } catch (error) {
      console.error('ChatStoreAdapter: Error exporting chat history:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const chatStoreAdapter = new ChatStoreAdapter();
