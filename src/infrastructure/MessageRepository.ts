
import type { Msg, MessageContent, MessageRole, MessageStatus } from '@/core/types/chat';

export interface CreateMessageParams {
  chatId: string;
  content: MessageContent;
  role: MessageRole;
  short?: string;
  model?: string;
  timestamp?: number;
  status?: MessageStatus;
}

export interface UpdateMessageParams {
  messageId: string;
  content?: MessageContent;
  thinkContent?: string;
  status?: MessageStatus;
  short?: string;
}

export interface IMessageRepository {
  create(params: CreateMessageParams): Promise<Msg>;
  update(params: UpdateMessageParams): Promise<void>;
  findById(messageId: string): Promise<Msg | undefined>;
  findByChatId(chatId: string): Promise<Msg[]>;
  delete(messageId: string): Promise<void>;
}
