import { Message } from './Message';
import { MessageContent, MessageRole, MessageStatus } from '@/core/types/chat';
import { hasThinkingContent, isThinkingCompleted, processContent } from '@/core/utils/messageUtils';

/**
 * 消息领域服务
 * 
 * 职责：
 * 1. 提供消息相关的业务规则和验证
 * 2. 处理复杂的消息业务逻辑
 * 3. 协调多个消息实体的交互
 * 4. 实现消息内容处理和转换
 */
export class MessageDomainService {
  
  /**
   * 验证消息内容是否有效
   */
  public validateMessageContent(content: MessageContent, role: MessageRole): boolean {
    if (role === 'user') {
      // 用户消息不能为空
      if (!content) {
        return false;
      }

      if (typeof content === 'string') {
        return content.trim().length > 0;
      }

      if (Array.isArray(content)) {
        // 检查数组内容，至少要有一个有效的字符串或图片
        const hasValidString = content.some(item =>
          typeof item === 'string' && item.trim().length > 0
        );
        const hasValidImage = content.some(item =>
          typeof item === 'object' && item.type === 'image' && item.imageId
        );

        return hasValidString || hasValidImage;
      }

      if (typeof content === 'object' && content.type === 'image') {
        // 单个图片内容
        return !!content.imageId;
      }
    }

    // 助手消息可以为空（在生成过程中）
    return true;
  }

  /**
   * 计算消息的token数量
   */
  public calculateMessageTokens(content: MessageContent): number {
    let textContent = '';

    if (typeof content === 'string') {
      textContent = content;
    } else if (Array.isArray(content)) {
      // 提取数组中的所有字符串内容
      textContent = content
        .filter(item => typeof item === 'string')
        .join(' ');
    } else if (typeof content === 'object' && content.type === 'image') {
      // 图片内容，使用alt文本或默认token数
      textContent = (content as any).alt || '';
    }

    // 简单的token估算：
    // - 中文字符：1个字符 ≈ 1.5个token
    // - 英文单词：1个单词 ≈ 1.3个token
    // - 标点符号：1个符号 ≈ 1个token
    
    const chineseChars = (textContent.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (textContent.match(/[a-zA-Z]+/g) || []).length;
    const punctuation = (textContent.match(/[^\w\s\u4e00-\u9fff]/g) || []).length;
    
    return Math.ceil(chineseChars * 1.5 + englishWords * 1.3 + punctuation);
  }

  /**
   * 处理思维链内容
   */
  public processThinkingContent(content: string): {
    mainContent: string;
    thinkContent: string | undefined;
    hasThinking: boolean;
    isThinkingCompleted: boolean;
  } {
    const { mainContent, thinkContent } = processContent(content);
    
    return {
      mainContent,
      thinkContent,
      hasThinking: hasThinkingContent(content),
      isThinkingCompleted: isThinkingCompleted(content),
    };
  }

  /**
   * 确定消息状态基于内容
   */
  public determineMessageStatus(content: string, currentStatus: MessageStatus): MessageStatus {
    const { hasThinking, isThinkingCompleted } = this.processThinkingContent(content);

    // 如果当前是等待状态
    if (currentStatus === MessageStatus.WAITING) {
      if (hasThinking && !isThinkingCompleted) {
        return MessageStatus.THINKING;
      } else if (content.trim()) {
        return MessageStatus.GENERATING;
      }
      return MessageStatus.WAITING;
    }

    // 如果当前是思考状态
    if (currentStatus === MessageStatus.THINKING) {
      if (isThinkingCompleted) {
        return MessageStatus.GENERATING;
      }
      return MessageStatus.THINKING;
    }

    // 如果当前是生成状态，保持生成状态
    if (currentStatus === MessageStatus.GENERATING) {
      return MessageStatus.GENERATING;
    }

    return currentStatus;
  }

  /**
   * 检查消息是否可以合并（用于流式更新优化）
   */
  public canMergeMessages(message1: Message, message2: Message): boolean {
    return message1.id === message2.id &&
           message1.chatId === message2.chatId &&
           message1.role === message2.role &&
           message1.role === 'assistant' && // 只有助手消息可以合并
           (message1.isProcessing() || message2.isProcessing());
  }

  /**
   * 合并消息内容（用于流式更新）
   */
  public mergeMessageContent(baseMessage: Message, newContent: string): Message {
    const currentContent = this.getMessageTextContent(baseMessage.content);

    const mergedContent = currentContent + newContent;
    const { mainContent, thinkContent } = this.processThinkingContent(mergedContent);

    // 创建新的消息实例
    const mergedMessage = new Message(
      baseMessage.id,
      baseMessage.chatId,
      mainContent,
      baseMessage.role,
      this.determineMessageStatus(mergedContent, baseMessage.status),
      baseMessage.timestamp,
      baseMessage.model,
      thinkContent
    );

    return mergedMessage;
  }

  /**
   * 获取消息内容的文本部分
   */
  private getMessageTextContent(content: MessageContent): string {
    if (typeof content === 'string') {
      return content;
    }

    if (Array.isArray(content)) {
      return content
        .filter(item => typeof item === 'string')
        .join(' ');
    }

    // 如果是ImageData，返回alt文本或空字符串
    return (content as any).alt || '';
  }

  /**
   * 验证消息序列的完整性
   */
  public validateMessageSequence(messages: Message[]): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (messages.length === 0) {
      return { isValid: true, errors: [] };
    }

    // 检查消息顺序
    for (let i = 0; i < messages.length - 1; i++) {
      const current = messages[i];
      const next = messages[i + 1];

      // 检查时间戳顺序
      if (current.timestamp > next.timestamp) {
        errors.push(`Message ${current.id} timestamp is after message ${next.id}`);
      }

      // 检查角色交替（可选规则）
      if (current.role === next.role && current.role === 'assistant') {
        // 连续的助手消息可能表示有问题
        console.warn(`Consecutive assistant messages: ${current.id} -> ${next.id}`);
      }
    }

    // 检查是否有未完成的消息（除了最后一条）
    for (let i = 0; i < messages.length - 1; i++) {
      const message = messages[i];
      if (message.isProcessing()) {
        errors.push(`Message ${message.id} is still processing but not the last message`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 生成消息摘要
   */
  public generateMessageSummary(message: Message): string {
    const content = this.getMessageTextContent(message.content);

    // 移除思维链内容
    const cleanContent = content.replace(/<think>[\s\S]*?<\/think>/g, '').trim();

    if (cleanContent.length <= 50) {
      return cleanContent;
    }

    // 尝试在句号、问号、感叹号处截断
    const sentences = cleanContent.split(/[。！？.!?]/);
    if (sentences.length > 1 && sentences[0].length <= 50) {
      return sentences[0] + (cleanContent.includes('。') ? '。' :
                           cleanContent.includes('！') ? '！' :
                           cleanContent.includes('？') ? '？' : '.');
    }

    // 否则简单截断
    return cleanContent.slice(0, 47) + '...';
  }

  /**
   * 检查消息是否包含敏感内容（简单实现）
   */
  public containsSensitiveContent(content: MessageContent): boolean {
    const textContent = this.getMessageTextContent(content);

    // 简单的敏感词检测（实际应用中应该使用更完善的过滤系统）
    const sensitiveWords = ['敏感词1', '敏感词2']; // 示例

    return sensitiveWords.some(word => textContent.includes(word));
  }

  /**
   * 格式化消息用于API调用
   */
  public formatMessageForAPI(message: Message): any {
    const content = typeof message.content === 'string' 
      ? message.content 
      : message.content;

    return {
      role: message.role,
      content: content,
    };
  }

  /**
   * 从API响应创建消息
   */
  public createMessageFromAPIResponse(
    id: string,
    chatId: string,
    apiResponse: any,
    model?: string
  ): Message {
    const content = apiResponse.content || apiResponse.message?.content || '';
    
    return new Message(
      id,
      chatId,
      content,
      'assistant',
      MessageStatus.COMPLETED,
      Date.now(),
      model
    );
  }
}
