import { MessageContent, MessageRole, MessageStatus } from '@/core/types/chat';

/**
 * Message 领域实体
 * 
 * 职责：
 * 1. 封装消息的核心业务逻辑
 * 2. 提供消息状态转换的业务规则
 * 3. 实现消息内容验证和处理
 * 4. 管理消息的生命周期
 */
export class Message {
  constructor(
    public readonly id: string,
    public readonly chatId: string,
    public content: MessageContent,
    public readonly role: MessageRole,
    public status: MessageStatus,
    public readonly timestamp: number = Date.now(),
    public model?: string,
    public thinkContent?: string,
    public short?: string
  ) {
    this.validateContent();
  }

  /**
   * 创建用户消息
   */
  static createUserMessage(
    id: string,
    chatId: string,
    content: MessageContent,
    timestamp?: number
  ): Message {
    return new Message(
      id,
      chatId,
      content,
      'user',
      MessageStatus.COMPLETED,
      timestamp
    );
  }

  /**
   * 创建AI助手消息占位符
   */
  static createAssistantPlaceholder(
    id: string,
    chatId: string,
    model?: string,
    timestamp?: number
  ): Message {
    return new Message(
      id,
      chatId,
      '',
      'assistant',
      MessageStatus.WAITING,
      timestamp,
      model
    );
  }

  /**
   * 更新消息内容
   */
  public updateContent(content: MessageContent): void {
    this.content = content;
    this.validateContent();
  }

  /**
   * 更新思考内容
   */
  public updateThinkContent(thinkContent: string): void {
    this.thinkContent = thinkContent;
  }

  /**
   * 更新消息状态
   */
  public updateStatus(status: MessageStatus): void {
    if (this.canTransitionTo(status)) {
      this.status = status;
    } else {
      throw new Error(`Invalid status transition from ${this.status} to ${status}`);
    }
  }

  /**
   * 标记消息为完成状态
   */
  public markAsCompleted(): void {
    this.updateStatus(MessageStatus.COMPLETED);
  }

  /**
   * 标记消息为错误状态
   */
  public markAsError(): void {
    this.updateStatus(MessageStatus.ERROR);
  }

  /**
   * 开始思考过程
   */
  public startThinking(): void {
    if (this.status === MessageStatus.WAITING) {
      this.updateStatus(MessageStatus.THINKING);
    }
  }

  /**
   * 开始生成内容
   */
  public startGenerating(): void {
    if (this.status === MessageStatus.THINKING || this.status === MessageStatus.WAITING) {
      this.updateStatus(MessageStatus.GENERATING);
    }
  }

  /**
   * 检查是否可以编辑
   */
  public canBeEdited(): boolean {
    return this.role === 'user' &&
           this.status !== MessageStatus.IDLE &&
           this.status !== MessageStatus.WAITING &&
           this.status !== MessageStatus.THINKING &&
           this.status !== MessageStatus.GENERATING;
  }

  /**
   * 检查是否可以删除
   */
  public canBeDeleted(): boolean {
    return this.status !== MessageStatus.IDLE &&
           this.status !== MessageStatus.WAITING &&
           this.status !== MessageStatus.THINKING &&
           this.status !== MessageStatus.GENERATING;
  }

  /**
   * 检查是否可以重发
   */
  public canBeResent(): boolean {
    return this.role === 'user' && this.status === MessageStatus.COMPLETED;
  }

  /**
   * 检查是否正在处理中
   */
  public isProcessing(): boolean {
    return this.status === MessageStatus.IDLE ||
           this.status === MessageStatus.WAITING ||
           this.status === MessageStatus.THINKING ||
           this.status === MessageStatus.GENERATING;
  }

  /**
   * 检查是否已完成
   */
  public isCompleted(): boolean {
    return this.status === MessageStatus.COMPLETED;
  }

  /**
   * 检查是否有错误
   */
  public hasError(): boolean {
    return this.status === MessageStatus.ERROR;
  }

  /**
   * 检查是否包含思考内容
   */
  public hasThinkingContent(): boolean {
    return !!this.thinkContent && this.thinkContent.trim().length > 0;
  }

  /**
   * 获取消息预览文本
   */
  public getPreviewText(): string {
    if (this.short) {
      return this.short;
    }

    const contentText = this.getContentText();
    return contentText.slice(0, 50) + (contentText.length > 50 ? '...' : '');
  }

  /**
   * 计算消息的token数量（简单估算）
   */
  public estimateTokenCount(): number {
    const contentText = this.getContentText();

    // 简单的token估算：大约4个字符 = 1个token
    return Math.ceil(contentText.length / 4);
  }

  /**
   * 获取消息的文本内容
   */
  private getContentText(): string {
    if (typeof this.content === 'string') {
      return this.content;
    }

    if (Array.isArray(this.content)) {
      // 如果是数组，提取所有字符串内容
      return this.content
        .filter(item => typeof item === 'string')
        .join(' ');
    }

    // 如果是ImageData，返回alt文本或空字符串
    return (this.content as any).alt || '';
  }

  /**
   * 验证状态转换是否合法
   */
  private canTransitionTo(newStatus: MessageStatus): boolean {
    const validTransitions: Record<MessageStatus, MessageStatus[]> = {
      [MessageStatus.IDLE]: [MessageStatus.WAITING],
      [MessageStatus.WAITING]: [MessageStatus.THINKING, MessageStatus.GENERATING, MessageStatus.ERROR, MessageStatus.COMPLETED],
      [MessageStatus.THINKING]: [MessageStatus.GENERATING, MessageStatus.ERROR, MessageStatus.COMPLETED],
      [MessageStatus.GENERATING]: [MessageStatus.COMPLETED, MessageStatus.ERROR],
      [MessageStatus.COMPLETED]: [MessageStatus.IDLE], // 允许重发
      [MessageStatus.ERROR]: [MessageStatus.IDLE, MessageStatus.WAITING], // 允许重试
    };

    return validTransitions[this.status]?.includes(newStatus) ?? false;
  }

  /**
   * 验证消息内容
   */
  private validateContent(): void {
    if (this.role === 'user' && !this.content) {
      throw new Error('User message content cannot be empty');
    }

    if (Array.isArray(this.content)) {
      // 验证数组内容
      for (const item of this.content) {
        if (typeof item === 'object' && item.type === 'image') {
          if (!item.imageId) {
            throw new Error('Image must have imageId');
          }
        }
      }
    }
  }

  /**
   * 转换为普通对象（用于序列化）
   */
  public toPlainObject() {
    return {
      id: this.id,
      chatId: this.chatId,
      content: this.content,
      role: this.role,
      status: this.status,
      timestamp: this.timestamp,
      model: this.model,
      thinkContent: this.thinkContent,
      short: this.short || this.getPreviewText(),
    };
  }

  /**
   * 从普通对象创建Message实例
   */
  static fromPlainObject(obj: any): Message {
    return new Message(
      obj.id,
      obj.chatId,
      obj.content,
      obj.role,
      obj.status,
      obj.timestamp,
      obj.model,
      obj.thinkContent,
      obj.short
    );
  }
}
