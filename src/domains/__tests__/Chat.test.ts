import { describe, it, expect, beforeEach } from 'vitest';
import { Chat } from '../chat/Chat';
import { Message } from '../message/Message';
import { MessageStatus } from '@/core/types/chat';

describe('Chat Domain Entity', () => {
  describe('constructor', () => {
    it('should create a chat with valid parameters', () => {
      const chat = new Chat('chat-1', 'Test Chat');

      expect(chat.id).toBe('chat-1');
      expect(chat.title).toBe('Test Chat');
      expect(chat.createdAt).toBeTypeOf('number');
      expect(chat.updatedAt).toBeTypeOf('number');
      expect(chat.messageCount).toBe(0);
      expect(chat.lastMessagePreview).toBe('新的对话');
    });

    it('should validate title length', () => {
      expect(() => {
        new Chat('chat-1', 'A'.repeat(101));
      }).toThrow('Chat title cannot exceed 100 characters');
    });
  });

  describe('static factory methods', () => {
    it('should create new chat', () => {
      const chat = Chat.createNew('chat-1', 'New Chat');

      expect(chat.id).toBe('chat-1');
      expect(chat.title).toBe('New Chat');
      expect(chat.isEmpty()).toBe(true);
    });

    it('should create new chat with default title', () => {
      const chat = Chat.createNew('chat-1');

      expect(chat.title).toBe('');
    });
  });

  describe('message management', () => {
    let chat: Chat;
    let message1: Message;
    let message2: Message;

    beforeEach(() => {
      chat = new Chat('chat-1', 'Test Chat');
      message1 = new Message('msg-1', 'chat-1', 'Hello', 'user', MessageStatus.COMPLETED);
      message2 = new Message('msg-2', 'chat-1', 'Hi there', 'assistant', MessageStatus.COMPLETED);
    });

    it('should add messages correctly', () => {
      chat.addMessage(message1);
      chat.addMessage(message2);

      expect(chat.messages.length).toBe(2);
      expect(chat.messageCount).toBe(2);
      expect(chat.lastMessagePreview).toBe('Hi there');
    });

    it('should reject messages with wrong chatId', () => {
      const wrongMessage = new Message('msg-1', 'wrong-chat', 'Hello', 'user', MessageStatus.COMPLETED);

      expect(() => {
        chat.addMessage(wrongMessage);
      }).toThrow('Message chatId does not match chat id');
    });

    it('should update messages correctly', () => {
      chat.addMessage(message1);
      
      const updatedMessage = new Message('msg-1', 'chat-1', 'Updated content', 'user', MessageStatus.COMPLETED);
      chat.updateMessage(updatedMessage);

      expect(chat.messages[0].content).toBe('Updated content');
    });

    it('should throw error when updating non-existent message', () => {
      expect(() => {
        chat.updateMessage(message1);
      }).toThrow('Message with id msg-1 not found in chat');
    });

    it('should remove messages correctly', () => {
      chat.addMessage(message1);
      chat.addMessage(message2);

      chat.removeMessage('msg-1');

      expect(chat.messages.length).toBe(1);
      expect(chat.messageCount).toBe(1);
      expect(chat.messages[0].id).toBe('msg-2');
    });

    it('should remove messages from specific point', () => {
      const message3 = new Message('msg-3', 'chat-1', 'Third', 'user', MessageStatus.COMPLETED);
      chat.addMessage(message1);
      chat.addMessage(message2);
      chat.addMessage(message3);

      const removedMessages = chat.removeMessagesFrom('msg-2');

      expect(chat.messages.length).toBe(1);
      expect(removedMessages.length).toBe(2);
      expect(removedMessages[0].id).toBe('msg-2');
      expect(removedMessages[1].id).toBe('msg-3');
    });

    it('should update last message preview when removing last message', () => {
      chat.addMessage(message1);
      chat.addMessage(message2);

      chat.removeMessage('msg-2');

      expect(chat.lastMessagePreview).toBe('Hello');
    });

    it('should reset preview when removing all messages', () => {
      chat.addMessage(message1);
      chat.removeMessage('msg-1');

      expect(chat.lastMessagePreview).toBe('新的对话');
    });
  });

  describe('message queries', () => {
    let chat: Chat;

    beforeEach(() => {
      chat = new Chat('chat-1', 'Test Chat');
      
      const userMsg1 = new Message('msg-1', 'chat-1', 'User 1', 'user', MessageStatus.COMPLETED);
      const assistantMsg1 = new Message('msg-2', 'chat-1', 'Assistant 1', 'assistant', MessageStatus.COMPLETED);
      const userMsg2 = new Message('msg-3', 'chat-1', 'User 2', 'user', MessageStatus.COMPLETED);
      const processingMsg = new Message('msg-4', 'chat-1', '', 'assistant', MessageStatus.GENERATING);

      chat.addMessage(userMsg1);
      chat.addMessage(assistantMsg1);
      chat.addMessage(userMsg2);
      chat.addMessage(processingMsg);
    });

    it('should get messages by role', () => {
      const userMessages = chat.getMessagesByRole('user');
      const assistantMessages = chat.getMessagesByRole('assistant');

      expect(userMessages.length).toBe(2);
      expect(assistantMessages.length).toBe(2);
      expect(userMessages[0].content).toBe('User 1');
      expect(userMessages[1].content).toBe('User 2');
    });

    it('should get last message', () => {
      const lastMessage = chat.getLastMessage();

      expect(lastMessage?.id).toBe('msg-4');
      expect(lastMessage?.status).toBe(MessageStatus.GENERATING);
    });

    it('should get last user message', () => {
      const lastUserMessage = chat.getLastUserMessage();

      expect(lastUserMessage?.id).toBe('msg-3');
      expect(lastUserMessage?.content).toBe('User 2');
    });

    it('should get last assistant message', () => {
      const lastAssistantMessage = chat.getLastAssistantMessage();

      expect(lastAssistantMessage?.id).toBe('msg-4');
      expect(lastAssistantMessage?.status).toBe(MessageStatus.GENERATING);
    });

    it('should detect processing messages', () => {
      expect(chat.hasProcessingMessage()).toBe(true);

      const processingMessage = chat.getProcessingMessage();
      expect(processingMessage?.id).toBe('msg-4');
    });

    it('should check if can send message', () => {
      expect(chat.canSendMessage()).toBe(false); // Has processing message

      // Complete the processing message
      const completedMessage = new Message('msg-4', 'chat-1', 'Completed', 'assistant', MessageStatus.COMPLETED);
      chat.updateMessage(completedMessage);

      expect(chat.canSendMessage()).toBe(true);
    });
  });

  describe('title management', () => {
    it('should update title', async () => {
      const chat = new Chat('chat-1', 'Old Title');

      // Wait a bit to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 1));

      chat.updateTitle('New Title');

      expect(chat.title).toBe('New Title');
      expect(chat.updatedAt).toBeGreaterThan(chat.createdAt);
    });

    it('should trim title when updating', () => {
      const chat = new Chat('chat-1', 'Old Title');

      chat.updateTitle('  New Title  ');

      expect(chat.title).toBe('New Title');
    });

    it('should generate summary title', () => {
      const chat = new Chat('chat-1', '');
      const userMessage = new Message('msg-1', 'chat-1', 'This is a long message that should be truncated', 'user', MessageStatus.COMPLETED);
      
      chat.addMessage(userMessage);

      const summaryTitle = chat.generateSummaryTitle();

      expect(summaryTitle).toBe('This is a long messa...');
      expect(summaryTitle.length).toBeLessThanOrEqual(23); // 20 chars + '...'
    });

    it('should handle thinking content in summary title', () => {
      const chat = new Chat('chat-1', '');
      const userMessage = new Message('msg-1', 'chat-1', '<think>thinking...</think>Actual content', 'user', MessageStatus.COMPLETED);
      
      chat.addMessage(userMessage);

      const summaryTitle = chat.generateSummaryTitle();

      expect(summaryTitle).toBe('Actual content');
      expect(summaryTitle).not.toContain('<think>');
    });

    it('should check if should auto generate title', () => {
      const chat = new Chat('chat-1', '');

      expect(chat.shouldAutoGenerateTitle()).toBe(false); // No messages

      const userMessage = new Message('msg-1', 'chat-1', 'Hello', 'user', MessageStatus.COMPLETED);
      const assistantMessage = new Message('msg-2', 'chat-1', 'Hi', 'assistant', MessageStatus.COMPLETED);
      
      chat.addMessage(userMessage);
      expect(chat.shouldAutoGenerateTitle()).toBe(false); // Only 1 message

      chat.addMessage(assistantMessage);
      expect(chat.shouldAutoGenerateTitle()).toBe(true); // 2+ messages and empty title
    });
  });

  describe('statistics', () => {
    let chat: Chat;

    beforeEach(() => {
      chat = new Chat('chat-1', 'Test Chat');
      
      const userMsg = new Message('msg-1', 'chat-1', 'User message', 'user', MessageStatus.COMPLETED);
      const assistantMsg = new Message('msg-2', 'chat-1', 'Assistant message', 'assistant', MessageStatus.COMPLETED);
      const errorMsg = new Message('msg-3', 'chat-1', '', 'assistant', MessageStatus.ERROR);

      chat.addMessage(userMsg);
      chat.addMessage(assistantMsg);
      chat.addMessage(errorMsg);
    });

    it('should calculate token count', () => {
      const totalTokens = chat.estimateTotalTokens();

      expect(totalTokens).toBeGreaterThan(0);
    });

    it('should generate statistics', () => {
      const stats = chat.getStatistics();

      expect(stats.totalMessages).toBe(3);
      expect(stats.userMessages).toBe(1);
      expect(stats.assistantMessages).toBe(2);
      expect(stats.completedMessages).toBe(2);
      expect(stats.errorMessages).toBe(1);
      expect(stats.estimatedTokens).toBeGreaterThan(0);
      expect(stats.createdAt).toBe(chat.createdAt);
      expect(stats.updatedAt).toBe(chat.updatedAt);
    });
  });

  describe('serialization', () => {
    it('should convert to plain object', () => {
      const chat = new Chat('chat-1', 'Test Chat', 12345, 12346, 2, 'Last message');
      const message = new Message('msg-1', 'chat-1', 'Hello', 'user', MessageStatus.COMPLETED);
      chat.setMessages([message]);

      const plainObject = chat.toPlainObject();

      expect(plainObject).toEqual({
        id: 'chat-1',
        short: 'Test Chat',
        time: 12345,
        lastMsgPreview: 'Hello', // Updated from message
        messageCount: 1, // Updated from messages
        msgList: [message.toPlainObject()],
      });
    });

    it('should create from plain object', () => {
      const plainObject = {
        id: 'chat-1',
        short: 'Test Chat',
        time: 12345,
        lastMsgPreview: 'Last message',
        messageCount: 1,
        msgList: [{
          id: 'msg-1',
          chatId: 'chat-1',
          content: 'Hello',
          role: 'user',
          status: MessageStatus.COMPLETED,
          timestamp: 12345,
        }],
      };

      const chat = Chat.fromPlainObject(plainObject);

      expect(chat.id).toBe('chat-1');
      expect(chat.title).toBe('Test Chat');
      expect(chat.createdAt).toBe(12345);
      expect(chat.messages.length).toBe(1);
      expect(chat.messages[0].content).toBe('Hello');
    });

    it('should handle missing msgList in plain object', () => {
      const plainObject = {
        id: 'chat-1',
        short: 'Test Chat',
        time: 12345,
      };

      const chat = Chat.fromPlainObject(plainObject);

      expect(chat.messages.length).toBe(0);
      expect(chat.messageCount).toBe(0);
    });
  });

  describe('message validation', () => {
    it('should validate messages belong to chat when setting', () => {
      const chat = new Chat('chat-1', 'Test Chat');
      const wrongMessage = new Message('msg-1', 'wrong-chat', 'Hello', 'user', MessageStatus.COMPLETED);

      expect(() => {
        chat.setMessages([wrongMessage]);
      }).toThrow('Message msg-1 does not belong to chat chat-1');
    });

    it('should set messages correctly', () => {
      const chat = new Chat('chat-1', 'Test Chat');
      const message1 = new Message('msg-1', 'chat-1', 'Hello', 'user', MessageStatus.COMPLETED);
      const message2 = new Message('msg-2', 'chat-1', 'Hi', 'assistant', MessageStatus.COMPLETED);

      chat.setMessages([message1, message2]);

      expect(chat.messages.length).toBe(2);
      expect(chat.messageCount).toBe(2);
      expect(chat.lastMessagePreview).toBe('Hi');
    });
  });
});
