import { describe, it, expect } from 'vitest';
import { Message } from '../message/Message';
import { MessageStatus } from '@/core/types/chat';

describe('Message Domain Entity', () => {
  describe('constructor', () => {
    it('should create a message with valid parameters', () => {
      const message = new Message(
        'msg-1',
        'chat-1',
        'Hello world',
        'user',
        MessageStatus.COMPLETED
      );

      expect(message.id).toBe('msg-1');
      expect(message.chatId).toBe('chat-1');
      expect(message.content).toBe('Hello world');
      expect(message.role).toBe('user');
      expect(message.status).toBe(MessageStatus.COMPLETED);
      expect(message.timestamp).toBeTypeOf('number');
    });

    it('should throw error for empty user message content', () => {
      expect(() => {
        new Message('msg-1', 'chat-1', '', 'user', MessageStatus.COMPLETED);
      }).toThrow('User message content cannot be empty');
    });

    it('should allow empty content for assistant messages', () => {
      expect(() => {
        new Message('msg-1', 'chat-1', '', 'assistant', MessageStatus.WAITING);
      }).not.toThrow();
    });
  });

  describe('static factory methods', () => {
    it('should create user message correctly', () => {
      const message = Message.createUserMessage('msg-1', 'chat-1', 'User input');

      expect(message.role).toBe('user');
      expect(message.content).toBe('User input');
      expect(message.status).toBe(MessageStatus.COMPLETED);
    });

    it('should create assistant placeholder correctly', () => {
      const message = Message.createAssistantPlaceholder('msg-1', 'chat-1', 'gpt-4');

      expect(message.role).toBe('assistant');
      expect(message.content).toBe('');
      expect(message.status).toBe(MessageStatus.WAITING);
      expect(message.model).toBe('gpt-4');
    });
  });

  describe('content updates', () => {
    it('should update content successfully', () => {
      const message = new Message('msg-1', 'chat-1', 'Initial', 'assistant', MessageStatus.GENERATING);
      
      message.updateContent('Updated content');
      
      expect(message.content).toBe('Updated content');
    });

    it('should update think content', () => {
      const message = new Message('msg-1', 'chat-1', '', 'assistant', MessageStatus.THINKING);
      
      message.updateThinkContent('Thinking about this...');
      
      expect(message.thinkContent).toBe('Thinking about this...');
    });
  });

  describe('status transitions', () => {
    it('should allow valid status transitions', () => {
      const message = new Message('msg-1', 'chat-1', '', 'assistant', MessageStatus.WAITING);
      
      expect(() => message.updateStatus(MessageStatus.THINKING)).not.toThrow();
      expect(message.status).toBe(MessageStatus.THINKING);
      
      expect(() => message.updateStatus(MessageStatus.GENERATING)).not.toThrow();
      expect(message.status).toBe(MessageStatus.GENERATING);
      
      expect(() => message.updateStatus(MessageStatus.COMPLETED)).not.toThrow();
      expect(message.status).toBe(MessageStatus.COMPLETED);
    });

    it('should reject invalid status transitions', () => {
      const message = new Message('msg-1', 'chat-1', '', 'assistant', MessageStatus.COMPLETED);
      
      expect(() => message.updateStatus(MessageStatus.THINKING)).toThrow();
    });

    it('should allow resend from completed status', () => {
      const message = new Message('msg-1', 'chat-1', 'Content', 'user', MessageStatus.COMPLETED);
      
      expect(() => message.updateStatus(MessageStatus.IDLE)).not.toThrow();
    });
  });

  describe('convenience methods', () => {
    it('should mark as completed', () => {
      const message = new Message('msg-1', 'chat-1', '', 'assistant', MessageStatus.GENERATING);
      
      message.markAsCompleted();
      
      expect(message.status).toBe(MessageStatus.COMPLETED);
    });

    it('should mark as error', () => {
      const message = new Message('msg-1', 'chat-1', '', 'assistant', MessageStatus.GENERATING);
      
      message.markAsError();
      
      expect(message.status).toBe(MessageStatus.ERROR);
    });

    it('should start thinking', () => {
      const message = new Message('msg-1', 'chat-1', '', 'assistant', MessageStatus.WAITING);
      
      message.startThinking();
      
      expect(message.status).toBe(MessageStatus.THINKING);
    });

    it('should start generating', () => {
      const message = new Message('msg-1', 'chat-1', '', 'assistant', MessageStatus.THINKING);
      
      message.startGenerating();
      
      expect(message.status).toBe(MessageStatus.GENERATING);
    });
  });

  describe('permission checks', () => {
    it('should check if user message can be edited', () => {
      const userMessage = new Message('msg-1', 'chat-1', 'Content', 'user', MessageStatus.COMPLETED);
      const assistantMessage = new Message('msg-2', 'chat-1', 'Content', 'assistant', MessageStatus.COMPLETED);
      
      expect(userMessage.canBeEdited()).toBe(true);
      expect(assistantMessage.canBeEdited()).toBe(false);
    });

    it('should check if processing message can be edited', () => {
      const message = new Message('msg-1', 'chat-1', 'Content', 'user', MessageStatus.WAITING);
      
      expect(message.canBeEdited()).toBe(false);
    });

    it('should check if message can be deleted', () => {
      const completedMessage = new Message('msg-1', 'chat-1', 'Content', 'user', MessageStatus.COMPLETED);
      const processingMessage = new Message('msg-2', 'chat-1', 'Content', 'assistant', MessageStatus.GENERATING);
      
      expect(completedMessage.canBeDeleted()).toBe(true);
      expect(processingMessage.canBeDeleted()).toBe(false);
    });

    it('should check if message can be resent', () => {
      const userMessage = new Message('msg-1', 'chat-1', 'Content', 'user', MessageStatus.COMPLETED);
      const assistantMessage = new Message('msg-2', 'chat-1', 'Content', 'assistant', MessageStatus.COMPLETED);
      
      expect(userMessage.canBeResent()).toBe(true);
      expect(assistantMessage.canBeResent()).toBe(false);
    });
  });

  describe('status queries', () => {
    it('should check if message is processing', () => {
      const waitingMessage = new Message('msg-1', 'chat-1', '', 'assistant', MessageStatus.WAITING);
      const thinkingMessage = new Message('msg-2', 'chat-1', '', 'assistant', MessageStatus.THINKING);
      const generatingMessage = new Message('msg-3', 'chat-1', '', 'assistant', MessageStatus.GENERATING);
      const completedMessage = new Message('msg-4', 'chat-1', 'Done', 'assistant', MessageStatus.COMPLETED);
      
      expect(waitingMessage.isProcessing()).toBe(true);
      expect(thinkingMessage.isProcessing()).toBe(true);
      expect(generatingMessage.isProcessing()).toBe(true);
      expect(completedMessage.isProcessing()).toBe(false);
    });

    it('should check if message is completed', () => {
      const completedMessage = new Message('msg-1', 'chat-1', 'Done', 'assistant', MessageStatus.COMPLETED);
      const processingMessage = new Message('msg-2', 'chat-1', '', 'assistant', MessageStatus.GENERATING);
      
      expect(completedMessage.isCompleted()).toBe(true);
      expect(processingMessage.isCompleted()).toBe(false);
    });

    it('should check if message has error', () => {
      const errorMessage = new Message('msg-1', 'chat-1', '', 'assistant', MessageStatus.ERROR);
      const normalMessage = new Message('msg-2', 'chat-1', 'Content', 'assistant', MessageStatus.COMPLETED);
      
      expect(errorMessage.hasError()).toBe(true);
      expect(normalMessage.hasError()).toBe(false);
    });

    it('should check if message has thinking content', () => {
      const messageWithThinking = new Message('msg-1', 'chat-1', '', 'assistant', MessageStatus.THINKING);
      messageWithThinking.updateThinkContent('Thinking...');
      
      const messageWithoutThinking = new Message('msg-2', 'chat-1', 'Content', 'assistant', MessageStatus.COMPLETED);
      
      expect(messageWithThinking.hasThinkingContent()).toBe(true);
      expect(messageWithoutThinking.hasThinkingContent()).toBe(false);
    });
  });

  describe('content processing', () => {
    it('should generate preview text', () => {
      const shortMessage = new Message('msg-1', 'chat-1', 'Short', 'user', MessageStatus.COMPLETED);
      const longMessage = new Message('msg-2', 'chat-1', 'A'.repeat(100), 'user', MessageStatus.COMPLETED);
      
      expect(shortMessage.getPreviewText()).toBe('Short');
      expect(longMessage.getPreviewText()).toBe('A'.repeat(50) + '...');
    });

    it('should use short property for preview if available', () => {
      const message = new Message('msg-1', 'chat-1', 'Long content', 'user', MessageStatus.COMPLETED, Date.now(), undefined, undefined, 'Custom short');
      
      expect(message.getPreviewText()).toBe('Custom short');
    });

    it('should estimate token count', () => {
      const message = new Message('msg-1', 'chat-1', 'Hello world', 'user', MessageStatus.COMPLETED);
      
      const tokenCount = message.estimateTokenCount();
      
      expect(tokenCount).toBeGreaterThan(0);
      expect(tokenCount).toBe(Math.ceil('Hello world'.length / 4));
    });
  });

  describe('array content handling', () => {
    it('should handle array content with strings', () => {
      const arrayContent = ['Hello', ' ', 'world'];
      const message = new Message('msg-1', 'chat-1', arrayContent, 'user', MessageStatus.COMPLETED);
      
      expect(message.getPreviewText()).toBe('Hello   world');
    });

    it('should handle array content with images', () => {
      const arrayContent = [
        'Text content',
        { type: 'image' as const, imageId: 1 }
      ];
      const message = new Message('msg-1', 'chat-1', arrayContent, 'user', MessageStatus.COMPLETED);

      expect(message.getPreviewText()).toBe('Text content');
    });

    it('should validate array content with images', () => {
      const invalidArrayContent = [
        'Text content',
        { type: 'image' as const } // Missing imageId
      ] as any;

      expect(() => {
        new Message('msg-1', 'chat-1', invalidArrayContent, 'user', MessageStatus.COMPLETED);
      }).toThrow('Image must have imageId');
    });
  });

  describe('serialization', () => {
    it('should convert to plain object', () => {
      const message = new Message(
        'msg-1',
        'chat-1',
        'Content',
        'user',
        MessageStatus.COMPLETED,
        12345,
        'gpt-4',
        'thinking...',
        'short'
      );
      
      const plainObject = message.toPlainObject();
      
      expect(plainObject).toEqual({
        id: 'msg-1',
        chatId: 'chat-1',
        content: 'Content',
        role: 'user',
        status: MessageStatus.COMPLETED,
        timestamp: 12345,
        model: 'gpt-4',
        thinkContent: 'thinking...',
        short: 'short',
      });
    });

    it('should create from plain object', () => {
      const plainObject = {
        id: 'msg-1',
        chatId: 'chat-1',
        content: 'Content',
        role: 'user',
        status: MessageStatus.COMPLETED,
        timestamp: 12345,
        model: 'gpt-4',
        thinkContent: 'thinking...',
        short: 'short',
      };
      
      const message = Message.fromPlainObject(plainObject);
      
      expect(message.id).toBe('msg-1');
      expect(message.content).toBe('Content');
      expect(message.role).toBe('user');
      expect(message.status).toBe(MessageStatus.COMPLETED);
      expect(message.thinkContent).toBe('thinking...');
    });
  });
});
