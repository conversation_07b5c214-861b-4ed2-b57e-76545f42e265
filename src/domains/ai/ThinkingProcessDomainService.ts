import { MessageStatus } from '@/core/types/chat';
import { hasThinkingContent, isThinkingCompleted, processContent } from '@/core/utils/messageUtils';

/**
 * 思维链处理领域服务
 * 
 * 职责：
 * 1. 处理AI思维链的解析和状态管理
 * 2. 提供思维链相关的业务规则
 * 3. 管理思维链的生命周期
 * 4. 实现思维链内容的验证和处理
 */
export class ThinkingProcessDomainService {

  /**
   * 解析思维链内容
   */
  public parseThinkingContent(content: string): {
    mainContent: string;
    thinkContent: string | undefined;
    hasThinking: boolean;
    isCompleted: boolean;
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    
    try {
      const { mainContent, thinkContent } = processContent(content);
      const hasThinking = hasThinkingContent(content);
      const isCompleted = isThinkingCompleted(content);

      // 验证思维链格式
      if (hasThinking) {
        const thinkMatches = content.match(/<think>/g);
        const thinkCloseMatches = content.match(/<\/think>/g);
        
        if (thinkMatches && thinkCloseMatches) {
          if (thinkMatches.length !== thinkCloseMatches.length) {
            errors.push('思维链标签不匹配：<think> 和 </think> 数量不一致');
          }
        } else if (thinkMatches && !thinkCloseMatches) {
          // 思维链还在进行中，这是正常的
        }

        // 检查嵌套的思维链标签
        if (thinkContent && thinkContent.includes('<think>')) {
          errors.push('思维链内容中不应包含嵌套的 <think> 标签');
        }
      }

      return {
        mainContent,
        thinkContent,
        hasThinking,
        isCompleted,
        isValid: errors.length === 0,
        errors,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(`解析思维链内容时发生错误: ${errorMessage}`);
      
      return {
        mainContent: content,
        thinkContent: undefined,
        hasThinking: false,
        isCompleted: false,
        isValid: false,
        errors,
      };
    }
  }

  /**
   * 确定基于思维链状态的消息状态
   */
  public determineMessageStatus(
    content: string,
    currentStatus: MessageStatus
  ): MessageStatus {
    const { hasThinking, isCompleted } = this.parseThinkingContent(content);

    // 状态转换逻辑
    switch (currentStatus) {
      case MessageStatus.WAITING:
        if (hasThinking && !isCompleted) {
          return MessageStatus.THINKING;
        } else if (content.trim() && (!hasThinking || isCompleted)) {
          return MessageStatus.GENERATING;
        }
        return MessageStatus.WAITING;

      case MessageStatus.THINKING:
        if (isCompleted) {
          return MessageStatus.GENERATING;
        }
        return MessageStatus.THINKING;

      case MessageStatus.GENERATING:
        // 一旦开始生成主内容，就保持生成状态
        return MessageStatus.GENERATING;

      default:
        return currentStatus;
    }
  }

  /**
   * 验证思维链内容的质量
   */
  public validateThinkingQuality(thinkContent: string): {
    isValid: boolean;
    score: number; // 0-100
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 100;

    if (!thinkContent || thinkContent.trim().length === 0) {
      return {
        isValid: false,
        score: 0,
        issues: ['思维链内容为空'],
        suggestions: ['思维链应该包含AI的思考过程'],
      };
    }

    // 检查长度
    if (thinkContent.length < 10) {
      issues.push('思维链内容过短');
      suggestions.push('思维链应该包含更详细的思考过程');
      score -= 20;
    } else if (thinkContent.length > 2000) {
      issues.push('思维链内容过长');
      suggestions.push('思维链应该更加简洁明了');
      score -= 10;
    }

    // 检查结构化程度
    const hasSteps = /步骤|第\d+|首先|然后|最后|接下来/.test(thinkContent);
    if (!hasSteps) {
      suggestions.push('建议使用步骤化的思考结构');
      score -= 5;
    }

    // 检查逻辑连贯性（简单检查）
    const hasLogicalWords = /因为|所以|因此|由于|导致|结果|原因/.test(thinkContent);
    if (!hasLogicalWords) {
      suggestions.push('建议增加逻辑连接词以提高连贯性');
      score -= 5;
    }

    // 检查是否包含具体分析
    const hasAnalysis = /分析|考虑|评估|比较|判断|推理/.test(thinkContent);
    if (!hasAnalysis) {
      suggestions.push('建议包含更多分析性思考');
      score -= 10;
    }

    return {
      isValid: issues.length === 0,
      score: Math.max(0, score),
      issues,
      suggestions,
    };
  }

  /**
   * 提取思维链中的关键信息
   */
  public extractKeyInsights(thinkContent: string): {
    keywords: string[];
    concepts: string[];
    decisions: string[];
    uncertainties: string[];
  } {
    const keywords: string[] = [];
    const concepts: string[] = [];
    const decisions: string[] = [];
    const uncertainties: string[] = [];

    if (!thinkContent) {
      return { keywords, concepts, decisions, uncertainties };
    }

    // 提取关键词（简单实现）
    const keywordPatterns = [
      /重要的是(.{1,20})/g,
      /关键在于(.{1,20})/g,
      /核心问题是(.{1,20})/g,
    ];

    keywordPatterns.forEach(pattern => {
      const matches = thinkContent.match(pattern);
      if (matches) {
        keywords.push(...matches.map(match => match.replace(pattern, '$1').trim()));
      }
    });

    // 提取概念
    const conceptPatterns = [
      /这是一个(.{1,15})问题/g,
      /涉及到(.{1,15})概念/g,
      /属于(.{1,15})范畴/g,
    ];

    conceptPatterns.forEach(pattern => {
      const matches = thinkContent.match(pattern);
      if (matches) {
        concepts.push(...matches.map(match => match.replace(pattern, '$1').trim()));
      }
    });

    // 提取决策点
    const decisionPatterns = [
      /我决定(.{1,30})/g,
      /我选择(.{1,30})/g,
      /最好的方法是(.{1,30})/g,
      /应该(.{1,30})/g,
    ];

    decisionPatterns.forEach(pattern => {
      const matches = thinkContent.match(pattern);
      if (matches) {
        decisions.push(...matches.map(match => match.replace(pattern, '$1').trim()));
      }
    });

    // 提取不确定性
    const uncertaintyPatterns = [
      /不确定(.{1,20})/g,
      /可能(.{1,20})/g,
      /也许(.{1,20})/g,
      /不太清楚(.{1,20})/g,
    ];

    uncertaintyPatterns.forEach(pattern => {
      const matches = thinkContent.match(pattern);
      if (matches) {
        uncertainties.push(...matches.map(match => match.replace(pattern, '$1').trim()));
      }
    });

    return {
      keywords: [...new Set(keywords)], // 去重
      concepts: [...new Set(concepts)],
      decisions: [...new Set(decisions)],
      uncertainties: [...new Set(uncertainties)],
    };
  }

  /**
   * 计算思维链的复杂度
   */
  public calculateComplexity(thinkContent: string): {
    complexity: 'low' | 'medium' | 'high';
    score: number; // 0-100
    factors: {
      length: number;
      structure: number;
      concepts: number;
      logic: number;
    };
  } {
    if (!thinkContent) {
      return {
        complexity: 'low',
        score: 0,
        factors: { length: 0, structure: 0, concepts: 0, logic: 0 },
      };
    }

    // 长度因子
    const lengthScore = Math.min(100, (thinkContent.length / 500) * 100);

    // 结构因子
    const structureWords = ['首先', '然后', '接下来', '最后', '步骤', '第一', '第二'];
    const structureCount = structureWords.reduce((count, word) => 
      count + (thinkContent.includes(word) ? 1 : 0), 0);
    const structureScore = Math.min(100, (structureCount / 3) * 100);

    // 概念因子
    const conceptWords = ['概念', '理论', '原理', '方法', '策略', '模式', '框架'];
    const conceptCount = conceptWords.reduce((count, word) => 
      count + (thinkContent.includes(word) ? 1 : 0), 0);
    const conceptScore = Math.min(100, (conceptCount / 3) * 100);

    // 逻辑因子
    const logicWords = ['因为', '所以', '因此', '由于', '导致', '如果', '那么'];
    const logicCount = logicWords.reduce((count, word) => 
      count + (thinkContent.includes(word) ? 1 : 0), 0);
    const logicScore = Math.min(100, (logicCount / 4) * 100);

    // 综合评分
    const totalScore = (lengthScore * 0.3 + structureScore * 0.3 + 
                       conceptScore * 0.2 + logicScore * 0.2);

    let complexity: 'low' | 'medium' | 'high';
    if (totalScore < 30) {
      complexity = 'low';
    } else if (totalScore < 70) {
      complexity = 'medium';
    } else {
      complexity = 'high';
    }

    return {
      complexity,
      score: Math.round(totalScore),
      factors: {
        length: Math.round(lengthScore),
        structure: Math.round(structureScore),
        concepts: Math.round(conceptScore),
        logic: Math.round(logicScore),
      },
    };
  }

  /**
   * 生成思维链摘要
   */
  public generateSummary(thinkContent: string): string {
    if (!thinkContent || thinkContent.trim().length === 0) {
      return '无思考内容';
    }

    const insights = this.extractKeyInsights(thinkContent);
    const complexity = this.calculateComplexity(thinkContent);

    let summary = '';

    // 添加复杂度信息
    summary += `思考复杂度: ${complexity.complexity === 'low' ? '简单' : 
                              complexity.complexity === 'medium' ? '中等' : '复杂'}\n`;

    // 添加关键决策
    if (insights.decisions.length > 0) {
      summary += `主要决策: ${insights.decisions.slice(0, 2).join(', ')}\n`;
    }

    // 添加关键概念
    if (insights.concepts.length > 0) {
      summary += `涉及概念: ${insights.concepts.slice(0, 3).join(', ')}\n`;
    }

    // 添加不确定性
    if (insights.uncertainties.length > 0) {
      summary += `不确定因素: ${insights.uncertainties.slice(0, 2).join(', ')}\n`;
    }

    return summary.trim() || '思考过程较为简单';
  }

  /**
   * 检查思维链是否应该被隐藏
   */
  public shouldHideThinking(thinkContent: string, userPreference: 'always' | 'auto' | 'never'): boolean {
    if (userPreference === 'always') {
      return true;
    }
    
    if (userPreference === 'never') {
      return false;
    }

    // 自动模式：根据内容质量决定
    const quality = this.validateThinkingQuality(thinkContent);
    
    // 如果质量分数低于50，建议隐藏
    return quality.score < 50;
  }
}
