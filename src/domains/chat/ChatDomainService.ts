import { Chat } from './Chat';
import { Message } from '../message/Message';
import { MessageRole, MessageContent } from '@/core/types/chat';

/**
 * 聊天领域服务
 * 
 * 职责：
 * 1. 提供聊天相关的业务规则和验证
 * 2. 处理复杂的聊天业务逻辑
 * 3. 协调聊天和消息的交互
 * 4. 实现聊天摘要和分析功能
 */
export class ChatDomainService {

  /**
   * 验证聊天标题是否有效
   */
  public validateChatTitle(title: string): {
    isValid: boolean;
    error?: string;
  } {
    const trimmedTitle = title.trim();

    if (trimmedTitle.length === 0) {
      return { isValid: true }; // 空标题是允许的，会自动生成
    }

    if (trimmedTitle.length > 100) {
      return {
        isValid: false,
        error: '聊天标题不能超过100个字符',
      };
    }

    // 检查是否包含特殊字符
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(trimmedTitle)) {
      return {
        isValid: false,
        error: '聊天标题不能包含特殊字符 < > : " / \\ | ? *',
      };
    }

    return { isValid: true };
  }

  /**
   * 生成聊天的自动标题
   */
  public generateAutoTitle(chat: Chat): string {
    if (chat.isEmpty()) {
      return '新的对话';
    }

    // 使用第一条用户消息生成标题
    const firstUserMessage = chat.getMessagesByRole('user')[0];
    if (!firstUserMessage) {
      return '新的对话';
    }

    const content = this.getMessageTextContent(firstUserMessage.content);
    
    // 移除 <think></think> 标签内容
    const cleanContent = content.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
    
    if (cleanContent.length === 0) {
      return '新的对话';
    }

    // 取前20个字符作为标题
    return cleanContent.slice(0, 20) + (cleanContent.length > 20 ? '...' : '');
  }

  /**
   * 检查聊天是否需要摘要
   */
  public shouldGenerateSummary(chat: Chat, maxMessages: number = 10): boolean {
    return chat.messages.length >= maxMessages;
  }

  /**
   * 生成聊天摘要内容
   */
  public generateSummaryContent(chat: Chat, maxMessages: number = 10): string {
    const messages = chat.messages;
    
    if (messages.length <= maxMessages) {
      return ''; // 不需要摘要
    }

    // 获取需要摘要的消息（除了最后几条）
    const messagesToSummarize = messages.slice(0, messages.length - maxMessages);
    
    let summaryContent = '以下是之前的对话摘要：\n\n';
    
    for (const message of messagesToSummarize) {
      const content = this.getMessageTextContent(message.content);

      // 移除思维链内容
      const cleanContent = content.replace(/<think>[\s\S]*?<\/think>/g, '').trim();

      if (cleanContent) {
        const roleText = message.role === 'user' ? '用户' : '助手';
        const preview = cleanContent.length > 100
          ? cleanContent.slice(0, 100) + '...'
          : cleanContent;
        summaryContent += `${roleText}: ${preview}\n\n`;
      }
    }

    return summaryContent;
  }

  /**
   * 计算聊天的活跃度分数
   */
  public calculateActivityScore(chat: Chat): number {
    const now = Date.now();
    const dayInMs = 24 * 60 * 60 * 1000;
    
    // 基础分数：消息数量
    let score = chat.messages.length * 10;
    
    // 时间衰减：最近的活动得分更高
    const daysSinceLastUpdate = (now - chat.updatedAt) / dayInMs;
    const timeDecay = Math.exp(-daysSinceLastUpdate / 7); // 7天半衰期
    score *= timeDecay;
    
    // 对话质量：用户和助手消息的平衡
    const userMessages = chat.getMessagesByRole('user').length;
    const assistantMessages = chat.getMessagesByRole('assistant').length;
    const balance = Math.min(userMessages, assistantMessages) / Math.max(userMessages, assistantMessages, 1);
    score *= (0.5 + balance * 0.5); // 平衡度影响分数
    
    return Math.round(score);
  }

  /**
   * 检查聊天是否可以删除
   */
  public canDeleteChat(chat: Chat): {
    canDelete: boolean;
    reason?: string;
  } {
    // 检查是否有正在处理的消息
    if (chat.hasProcessingMessage()) {
      return {
        canDelete: false,
        reason: '聊天中有正在处理的消息，无法删除',
      };
    }

    return { canDelete: true };
  }

  /**
   * 检查聊天是否可以导出
   */
  public canExportChat(chat: Chat): {
    canExport: boolean;
    reason?: string;
  } {
    if (chat.isEmpty()) {
      return {
        canExport: false,
        reason: '空聊天无法导出',
      };
    }

    // 检查是否有错误消息
    const errorMessages = chat.messages.filter(msg => msg.hasError());
    if (errorMessages.length > 0) {
      return {
        canExport: true,
        reason: `聊天中有 ${errorMessages.length} 条错误消息，导出时将被跳过`,
      };
    }

    return { canExport: true };
  }

  /**
   * 生成聊天导出数据
   */
  public generateExportData(chat: Chat): {
    title: string;
    createdAt: string;
    updatedAt: string;
    messageCount: number;
    messages: Array<{
      role: string;
      content: string;
      timestamp: string;
      model?: string;
    }>;
  } {
    const completedMessages = chat.messages.filter(msg => msg.isCompleted());
    
    return {
      title: chat.title || chat.generateSummaryTitle(),
      createdAt: new Date(chat.createdAt).toISOString(),
      updatedAt: new Date(chat.updatedAt).toISOString(),
      messageCount: completedMessages.length,
      messages: completedMessages.map(msg => ({
        role: msg.role,
        content: this.getMessageTextContent(msg.content),
        timestamp: new Date(msg.timestamp).toISOString(),
        model: msg.model,
      })),
    };
  }

  /**
   * 分析聊天统计信息
   */
  public analyzeChat(chat: Chat): {
    messageStats: {
      total: number;
      user: number;
      assistant: number;
      completed: number;
      errors: number;
    };
    timeStats: {
      createdAt: Date;
      updatedAt: Date;
      duration: number; // 毫秒
      durationText: string;
    };
    contentStats: {
      totalTokens: number;
      averageTokensPerMessage: number;
      longestMessage: number;
      shortestMessage: number;
    };
    activityScore: number;
  } {
    const messages = chat.messages;
    const userMessages = chat.getMessagesByRole('user');
    const assistantMessages = chat.getMessagesByRole('assistant');
    const completedMessages = messages.filter(msg => msg.isCompleted());
    const errorMessages = messages.filter(msg => msg.hasError());

    // 计算内容统计
    const tokenCounts = messages.map(msg => msg.estimateTokenCount());
    const totalTokens = tokenCounts.reduce((sum, count) => sum + count, 0);
    const averageTokens = messages.length > 0 ? totalTokens / messages.length : 0;
    const longestMessage = Math.max(...tokenCounts, 0);
    const shortestMessage = Math.min(...tokenCounts, 0);

    // 计算时间统计
    const duration = chat.updatedAt - chat.createdAt;
    const durationText = this.formatDuration(duration);

    return {
      messageStats: {
        total: messages.length,
        user: userMessages.length,
        assistant: assistantMessages.length,
        completed: completedMessages.length,
        errors: errorMessages.length,
      },
      timeStats: {
        createdAt: new Date(chat.createdAt),
        updatedAt: new Date(chat.updatedAt),
        duration,
        durationText,
      },
      contentStats: {
        totalTokens,
        averageTokensPerMessage: Math.round(averageTokens),
        longestMessage,
        shortestMessage: messages.length > 0 ? shortestMessage : 0,
      },
      activityScore: this.calculateActivityScore(chat),
    };
  }

  /**
   * 格式化时间间隔
   */
  private formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天${hours % 24}小时`;
    } else if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 比较两个聊天的相似度
   */
  public calculateSimilarity(chat1: Chat, chat2: Chat): number {
    // 简单的相似度计算：基于标题和第一条消息的相似度
    const title1 = chat1.title.toLowerCase();
    const title2 = chat2.title.toLowerCase();
    
    // 计算标题相似度（简单的字符串匹配）
    const titleSimilarity = this.calculateStringSimilarity(title1, title2);
    
    // 计算第一条消息的相似度
    const firstMsg1 = chat1.getMessagesByRole('user')[0];
    const firstMsg2 = chat2.getMessagesByRole('user')[0];
    
    let messageSimilarity = 0;
    if (firstMsg1 && firstMsg2) {
      const content1 = this.getMessageTextContent(firstMsg1.content);
      const content2 = this.getMessageTextContent(firstMsg2.content);

      messageSimilarity = this.calculateStringSimilarity(
        content1.toLowerCase(),
        content2.toLowerCase()
      );
    }
    
    // 综合相似度
    return (titleSimilarity * 0.3 + messageSimilarity * 0.7);
  }

  /**
   * 计算字符串相似度（简单实现）
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1;
    if (str1.length === 0 || str2.length === 0) return 0;
    
    // 使用最长公共子序列算法的简化版本
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    let matches = 0;
    for (let i = 0; i < shorter.length; i++) {
      if (longer.includes(shorter[i])) {
        matches++;
      }
    }
    
    return matches / longer.length;
  }

  /**
   * 获取消息内容的文本部分
   */
  private getMessageTextContent(content: MessageContent): string {
    if (typeof content === 'string') {
      return content;
    }

    if (Array.isArray(content)) {
      return content
        .filter(item => typeof item === 'string')
        .join(' ');
    }

    // 如果是ImageData，返回alt文本或空字符串
    return (content as any).alt || '';
  }
}
