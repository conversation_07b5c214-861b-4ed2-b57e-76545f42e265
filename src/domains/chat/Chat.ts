import { Message } from '../message/Message';
import { MessageRole, MessageStatus } from '@/core/types/chat';

/**
 * Chat 领域实体
 * 
 * 职责：
 * 1. 管理聊天会话的生命周期
 * 2. 实现聊天相关的业务规则
 * 3. 管理消息列表和聊天状态
 * 4. 提供聊天摘要和统计功能
 */
export class Chat {
  private _messages: Message[] = [];

  constructor(
    public readonly id: string,
    public title: string,
    public readonly createdAt: number = Date.now(),
    public updatedAt: number = Date.now(),
    public messageCount: number = 0,
    public lastMessagePreview: string = '新的对话'
  ) {
    this.validateTitle();
  }

  /**
   * 创建新的聊天会话
   */
  static createNew(id: string, title?: string): Chat {
    const defaultTitle = title || '';
    return new Chat(id, defaultTitle);
  }

  /**
   * 获取消息列表（只读）
   */
  public get messages(): readonly Message[] {
    return this._messages;
  }

  /**
   * 添加消息到聊天
   */
  public addMessage(message: Message): void {
    if (message.chatId !== this.id) {
      throw new Error('Message chatId does not match chat id');
    }

    this._messages.push(message);
    this.messageCount = this._messages.length;
    this.updateLastMessagePreview(message);
    this.updatedAt = Date.now();
  }

  /**
   * 更新消息
   */
  public updateMessage(updatedMessage: Message): void {
    const index = this._messages.findIndex(msg => msg.id === updatedMessage.id);
    if (index === -1) {
      throw new Error(`Message with id ${updatedMessage.id} not found in chat`);
    }

    this._messages[index] = updatedMessage;
    
    // 如果更新的是最后一条消息，更新预览
    if (index === this._messages.length - 1) {
      this.updateLastMessagePreview(updatedMessage);
    }
    
    this.updatedAt = Date.now();
  }

  /**
   * 移除消息
   */
  public removeMessage(messageId: string): void {
    const index = this._messages.findIndex(msg => msg.id === messageId);
    if (index === -1) {
      throw new Error(`Message with id ${messageId} not found in chat`);
    }

    this._messages.splice(index, 1);
    this.messageCount = this._messages.length;
    
    // 更新最后消息预览
    if (this._messages.length > 0) {
      this.updateLastMessagePreview(this._messages[this._messages.length - 1]);
    } else {
      this.lastMessagePreview = '新的对话';
    }
    
    this.updatedAt = Date.now();
  }

  /**
   * 移除指定消息及其之后的所有消息（用于重发功能）
   */
  public removeMessagesFrom(messageId: string): Message[] {
    const index = this._messages.findIndex(msg => msg.id === messageId);
    if (index === -1) {
      throw new Error(`Message with id ${messageId} not found in chat`);
    }

    const removedMessages = this._messages.splice(index);
    this.messageCount = this._messages.length;
    
    // 更新最后消息预览
    if (this._messages.length > 0) {
      this.updateLastMessagePreview(this._messages[this._messages.length - 1]);
    } else {
      this.lastMessagePreview = '新的对话';
    }
    
    this.updatedAt = Date.now();
    return removedMessages;
  }

  /**
   * 获取指定角色的消息
   */
  public getMessagesByRole(role: MessageRole): Message[] {
    return this._messages.filter(msg => msg.role === role);
  }

  /**
   * 获取最后一条消息
   */
  public getLastMessage(): Message | undefined {
    return this._messages[this._messages.length - 1];
  }

  /**
   * 获取最后一条用户消息
   */
  public getLastUserMessage(): Message | undefined {
    for (let i = this._messages.length - 1; i >= 0; i--) {
      if (this._messages[i].role === 'user') {
        return this._messages[i];
      }
    }
    return undefined;
  }

  /**
   * 获取最后一条助手消息
   */
  public getLastAssistantMessage(): Message | undefined {
    for (let i = this._messages.length - 1; i >= 0; i--) {
      if (this._messages[i].role === 'assistant') {
        return this._messages[i];
      }
    }
    return undefined;
  }

  /**
   * 检查是否有正在处理的消息
   */
  public hasProcessingMessage(): boolean {
    return this._messages.some(msg => msg.isProcessing());
  }

  /**
   * 获取正在处理的消息
   */
  public getProcessingMessage(): Message | undefined {
    return this._messages.find(msg => msg.isProcessing());
  }

  /**
   * 检查聊天是否为空
   */
  public isEmpty(): boolean {
    return this._messages.length === 0;
  }

  /**
   * 检查是否可以发送新消息
   */
  public canSendMessage(): boolean {
    return !this.hasProcessingMessage();
  }

  /**
   * 更新聊天标题
   */
  public updateTitle(newTitle: string): void {
    this.title = newTitle.trim();
    this.validateTitle();
    this.updatedAt = Date.now();
  }

  /**
   * 生成聊天摘要标题
   */
  public generateSummaryTitle(): string {
    if (this._messages.length === 0) {
      return '新的对话';
    }

    const firstUserMessage = this._messages.find(msg => msg.role === 'user');
    if (!firstUserMessage) {
      return '新的对话';
    }

    const content = typeof firstUserMessage.content === 'string' 
      ? firstUserMessage.content 
      : firstUserMessage.content.text || '';
    
    // 移除 <think></think> 标签内容
    const cleanContent = content.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
    
    // 取前20个字符作为标题
    return cleanContent.slice(0, 20) + (cleanContent.length > 20 ? '...' : '');
  }

  /**
   * 计算聊天的总token数量
   */
  public estimateTotalTokens(): number {
    return this._messages.reduce((total, msg) => total + msg.estimateTokenCount(), 0);
  }

  /**
   * 获取聊天统计信息
   */
  public getStatistics() {
    const userMessages = this.getMessagesByRole('user');
    const assistantMessages = this.getMessagesByRole('assistant');
    const completedMessages = this._messages.filter(msg => msg.isCompleted());
    const errorMessages = this._messages.filter(msg => msg.hasError());

    return {
      totalMessages: this._messages.length,
      userMessages: userMessages.length,
      assistantMessages: assistantMessages.length,
      completedMessages: completedMessages.length,
      errorMessages: errorMessages.length,
      estimatedTokens: this.estimateTotalTokens(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }

  /**
   * 检查是否需要自动生成标题
   */
  public shouldAutoGenerateTitle(): boolean {
    return !this.title.trim() && this._messages.length >= 2;
  }

  /**
   * 设置消息列表（用于从存储加载）
   */
  public setMessages(messages: Message[]): void {
    // 验证所有消息都属于这个聊天
    for (const message of messages) {
      if (message.chatId !== this.id) {
        throw new Error(`Message ${message.id} does not belong to chat ${this.id}`);
      }
    }

    this._messages = [...messages];
    this.messageCount = this._messages.length;
    
    if (this._messages.length > 0) {
      this.updateLastMessagePreview(this._messages[this._messages.length - 1]);
    }
  }

  /**
   * 更新最后消息预览
   */
  private updateLastMessagePreview(message: Message): void {
    this.lastMessagePreview = message.getPreviewText();
  }

  /**
   * 验证聊天标题
   */
  private validateTitle(): void {
    if (this.title.length > 100) {
      throw new Error('Chat title cannot exceed 100 characters');
    }
  }

  /**
   * 转换为普通对象（用于序列化）
   */
  public toPlainObject() {
    return {
      id: this.id,
      short: this.title,
      time: this.createdAt,
      lastMsgPreview: this.lastMessagePreview,
      messageCount: this.messageCount,
      msgList: this._messages.map(msg => msg.toPlainObject()),
    };
  }

  /**
   * 从普通对象创建Chat实例
   */
  static fromPlainObject(obj: any): Chat {
    const chat = new Chat(
      obj.id,
      obj.short || '',
      obj.time,
      obj.updated || obj.time,
      obj.messageCount || 0,
      obj.lastMsgPreview || '新的对话'
    );

    if (obj.msgList && Array.isArray(obj.msgList)) {
      const messages = obj.msgList.map((msgObj: any) => Message.fromPlainObject(msgObj));
      chat.setMessages(messages);
    }

    return chat;
  }
}
