import { useStorage } from "@vueuse/core";
import { computed, ComputedRef, ref } from "vue";
import { defineStore } from "pinia";
import type { Msg, Chat } from "@/core/types/chat.ts";

// 回复风格类型定义
export type ReplyStyle = "default" | "concise" | "detailed";

// 回复风格配置
export const REPLY_STYLE_PROMPTS: Record<ReplyStyle, string> = {
    default: "",
    concise: "请用简洁明了的方式回答，避免冗长的解释。",
    detailed: "请提供详细、全面的回答，包含相关的背景信息和具体示例。"
};

export const REPLY_STYLE_LABELS: Record<ReplyStyle, string> = {
    default: "默认",
    concise: "简洁",
    detailed: "详细"
};

export const useChatStore = defineStore("chat", () => {
    // --- 状态定义 ---
    const maxMsgCount = useStorage<number>("chat/maxMsgCount", 10);
    const enableAiSummary = useStorage<boolean>("chat/enableAiSummary", false);

    // 总结模型相关设置
    const enableSeparateSummaryModel = useStorage<boolean>("chat/enableSeparateSummaryModel", false);
    const summaryModel = useStorage<string>("chat/summaryModel", "");
    const summaryProvider = useStorage<string>("chat/summaryProvider", "");

    // 聊天状态
    const activeChatIdStorage = ref<string>("0");
    const chatList = ref<Chat[]>([]);

    // UI状态
    const isSidebarCollapsed = useStorage<boolean>("config/isSidebarCollapsed", false);
    const isLoadingMessages = ref(false);
    const isLoadingChats = ref(false);
    const isLoading = ref(false); // Global loading state for sending messages

    // 回复风格状态
    const replyStyle = useStorage<ReplyStyle>("chat/replyStyle", "default");

    // --- 状态更新方法 (纯状态管理，不包含业务逻辑) ---

    function setChatList(chats: Chat[]) {
        chatList.value = chats;
    }

    function addChatToList(chat: Chat) {
        chatList.value.push(chat);
    }

    function removeChatFromList(chatId: string) {
        const idx = chatList.value.findIndex((chat) => chat.id === chatId);
        if (idx !== -1) {
            chatList.value.splice(idx, 1);
        }
    }

    function updateChatInList(chatId: string, updates: Partial<Chat>) {
        const idx = chatList.value.findIndex((item) => item.id === chatId);
        if (idx > -1) {
            Object.assign(chatList.value[idx], updates);
        }
    }

    function setActiveChatId(chatId: string) {
        activeChatIdStorage.value = chatId;
    }

    function setLoadingStates(states: {
        isLoading?: boolean;
        isLoadingMessages?: boolean;
        isLoadingChats?: boolean;
    }) {
        if (states.isLoading !== undefined) isLoading.value = states.isLoading;
        if (states.isLoadingMessages !== undefined) isLoadingMessages.value = states.isLoadingMessages;
        if (states.isLoadingChats !== undefined) isLoadingChats.value = states.isLoadingChats;
    }

    // --- 消息状态管理 ---

    function addMessage(chatId: string, message: Msg) {
        const chat = chatList.value.find((c) => c.id === chatId);
        if (chat) {
            chat.msgList.push(message);
            chat.messageCount = (chat.messageCount || 0) + 1;
            chat.lastMsgPreview = message.short;
        }
    }

    function updateMessage(chatId: string, message: Msg) {
        console.log(`[ChatStore] updateMessage for chatId: ${chatId}, status: ${message.status}`, JSON.stringify(message));
        const chat = chatList.value.find((c) => c.id === chatId);
        if (chat) {
            const msgIndex = chat.msgList.findIndex((m) => m.id === message.id);
            if (msgIndex > -1) {
                const oldStatus = chat.msgList[msgIndex].status;
                // 使用 Vue 的响应式方式更新数组元素
                chat.msgList.splice(msgIndex, 1, message);
                console.log(`[ChatStore] Message updated - Status changed from ${oldStatus} to ${message.status}`);
                // If it's the last message, update preview
                if (msgIndex === chat.msgList.length - 1) {
                    chat.lastMsgPreview = message.short;
                }
            } else {
                console.warn(`[ChatStore] Message with id ${message.id} not found in chat ${chatId}`);
            }
        } else {
            console.warn(`[ChatStore] Chat with id ${chatId} not found`);
        }
    }

    function setMessageList(chatId: string, messages: Msg[]) {
        const chat = chatList.value.find((c) => c.id === chatId);
        if (chat) {
            chat.msgList = messages;
        }
    }

    function removeMessagesFromChat(chatId: string, messageIds: string[]) {
        const chat = chatList.value.find((c) => c.id === chatId);
        if (chat) {
            chat.msgList = chat.msgList.filter(msg => !messageIds.includes(msg.id));
            chat.messageCount = Math.max(0, (chat.messageCount || 0) - messageIds.length);
        }
    }



    // --- Computed Properties & Refs ---

    const activeChatId = computed(() => activeChatIdStorage.value);

    const activeChat: ComputedRef<Chat> = computed(() => {
        return (
            chatList.value.find((item) => item.id === activeChatId.value) || {
                id: "0",
                msgList: [],
                short: "",
                time: 0,
                messageCount: 0,
                lastMsgPreview: "",
            }
        );
    });

    const scrollTo = ref(
        (position = "bottom", onlyScrollWhenAtBottom = false) => {
            console.warn("未初始化滚动函数", position, onlyScrollWhenAtBottom);
        }
    );

    return {
        // --- 状态 ---
        chatList,
        activeChatId,
        isLoading,
        isLoadingMessages,
        isLoadingChats,
        isSidebarCollapsed,
        maxMsgCount,
        enableAiSummary,
        replyStyle,
        enableSeparateSummaryModel,
        summaryModel,
        summaryProvider,

        // --- 计算属性 ---
        activeChat,

        // --- 状态更新方法 ---
        setChatList,
        addChatToList,
        removeChatFromList,
        updateChatInList,
        setActiveChatId,
        setLoadingStates,

        // --- 消息状态管理 ---
        addMessage,
        updateMessage,
        setMessageList,
        removeMessagesFromChat,

        // --- UI 相关 ---
        scrollTo,
    };
});
