请修复思考模型功能中的以下4个具体问题，每修复一个问题就更新开发记录并进行构建测试验证：

**问题1: 思考完成后内容仍然消失**
- 现象：尽管已实施修复，但思考完成后思考区域仍然消失
- 要求：进一步调试并修复此问题
- 调试要求：在相关代码处添加console.log调试日志，包括：
    - 思考状态变化的监听逻辑
    - shouldShowThinkContent计算属性的值变化
    - isThinkingInProgress和isThinkExpanded的状态变化
    - 便于分析问题根因

**问题2: 思考动画播放速度与数据返回不同步**
- 现象：思考内容数据已完全返回，但动画仍在播放中
- 要求：当检测到思考内容结束标签`</think>`时，立即完成思考内容的播放动画
- 实现方式：修改ThinkingAnimation组件，检测到完整标签时跳过剩余动画，直接显示完整内容

**问题3: 思考区域背景色优化**
- 要求：为思考区域设置明显但协调的背景色
- 深色模式：使用比当前背景稍浅的黑色
- 浅色模式：使用比当前背景稍深的灰白色
- 实现方式：使用CSS自定义属性(CSS tokens)，确保与naive-ui主题系统兼容
- 参考：可使用`--card-color`、`--body-color`等主题变量进行调色 也可以新增合适的颜色

**问题4: 正文输出时思考区域光标问题**
- 现象：AI开始输出正文内容时，思考区域末尾仍显示闪烁光标
- 期望：当AI开始输出正文时，思考区域应停止显示光标动画
- 实现要求：根据消息状态和内容类型，智能控制光标的显示/隐藏

**验收标准：**
- 所有修改通过TypeScript类型检查和构建测试
- 添加的调试日志能清晰显示状态变化过程
- 思考区域在各种状态下都能正确显示和隐藏
- 动画播放与数据返回保持同步
- 背景色在深色/浅色主题下都有良好的视觉效果