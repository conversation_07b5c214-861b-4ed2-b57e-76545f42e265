# 指令
帮我完成以下需求，每完成一小步，就同步更新在 docs/6.27/开发记录.md 中。

# 需求

## 1. 思考内容实时识别和渲染
对于思考模型，目前在输出思考内容的过程中，思考内容未被正确识别，因为此时可能标签还未闭合。比如`<think>xxx`但没有`</think>`。这导致了思考输出过程中，思考内容没有按思考内容的样式渲染。

**具体要求：**
- 在标签未闭合的情况下，也能正确提取思考内容
- 实现实时渲染思考内容的样式，即使`<think>`标签尚未闭合
- 确保思考内容在输出过程中就能正确应用样式

## 2. 思考内容动效优化
思考内容的动效需要优化。当模型正在输出思考内容时，前端展示类似广告牌的文字滚动效果。

**具体要求：**
- 文字从左到右出现
- 超过一行内容时，文字滚动到下一行
- 行间应有过渡效果
- 实现类似广告牌的滚动显示效果

## 3. 会话标题生成优化
目前首条消息回复后会赋予会话标题，可能会使用回复内容作为标题，如果是思考模型，这可能导致标题含有`<think>`标签字样。

**具体要求：**
- 确保被赋予标题的是回答的内容，而不是思考过程
- 过滤掉`<think>`标签及其内容，只使用实际回答内容作为标题
- 保证标题的清洁性和可读性

## 4. 思考内容样式优化
思考内容的样式需要优化。

**具体要求：**
- 思考区域应该是浅色背景和深色文字
- 左侧无border样式
- 展示一个4个角星星图标
- 在思考过程中播放旋转动画
- 确保视觉效果与其他消息内容有明显区分



## 5. "重命名会话"弹窗样式优化
**问题描述：** chatlist中重命名会话的弹窗使用了绿色主题和直角样式，与应用的naive ui主题不一致。

**具体要求：**
- 分析当前弹窗实现方式和样式来源
- 替换为使用应用自定义的naive ui主题样式
- 确保弹窗的颜色、圆角、字体等与应用整体风格一致
- 保持弹窗的功能性不变，只优化视觉样式

## 6. 聊天界面自动滚动优化需求

**问题描述：**
在AI回复消息的过程中，当用户当前位于聊天滚动区域的底部时，随着回复内容的逐渐增长，用户的视窗位置会逐渐偏离底部，导致用户无法实时看到最新的回复内容。

**期望行为：**
1. **自动跟随条件：** 当用户位于聊天区域底部（距离底部阈值可设为50px以内）且AI正在回复时，界面应自动滚动以保持最新消息始终可见
2. **用户主动滚动检测：** 如果用户在AI回复过程中主动向上滚动查看历史消息，则停止自动滚动跟随
3. **恢复自动跟随：** 用户重新滚动到底部时，恢复自动跟随行为
4. **回复完成后：** AI回复结束后，如果用户仍在底部附近，确保最终滚动到完全底部

**技术实现要点：**
- 需要区分用户主动滚动和程序自动滚动
- 实时监测用户的滚动位置
- 在消息内容更新时智能判断是否需要自动滚动
- 考虑使用防抖机制避免频繁滚动操作影响性能

**验收标准：**
- AI回复时，用户在底部能始终看到最新内容
- 用户向上滚动查看历史消息时，不会被强制拉回底部
- 滚动行为流畅，无卡顿或跳跃现象
## 开发要求
- 每完成一个需求点，立即更新 docs/6.27/开发记录.md
- 在开发记录中详细记录实现方案、修改的文件和关键代码变更
- 确保所有修改都经过测试验证