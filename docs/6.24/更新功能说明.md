# 更新功能说明

## 功能概述

**注意：移动端（iOS/Android）不支持自更新功能，因为移动应用由应用商店分发和管理更新。**
应用启动后会自动静默检查和下载更新，下载完成后在 ChatHeader 的右侧会显示一个"升级"按钮。

## 实现细节

### 1. 状态管理 (useEnvStore)

新增了以下状态：

-   `updateAvailable`: 是否有更新可用
-   `updateDownloaded`: 更新是否已下载完成
-   `updateInstalling`: 是否正在安装更新
-   `isMobile`: 是否为移动平台（iOS/Android）
-   `isDesktop`: 是否为桌面平台（支持自更新）
-   `updateInfo`: 更新信息（版本、说明、日期）

### 2. 更新器 (updater.ts)

-   `checkAndDownloadUpdate()`: 检查并静默下载更新
-   `installUpdate()`: 安装已下载的更新并重启应用
-   `simulateUpdateAvailable()`: 测试函数，模拟有更新可用的情况

### 3. 界面更新 (AppHeader.vue)

在 AppHeader 组件中添加了升级按钮：

-   只有在 `updateDownloaded` 和 `isDesktop` 都为 true 时才显示
-   点击后调用 `installUpdate()` 安装更新
-   安装过程中显示加载状态和"升级中..."文本

### 4. 移除旧的更新提示 (ChatLayout.vue)

-   移除了旧的"版本 X.X.X 将在 5 秒后安装"通知
-   现在更新下载是完全静默的，只有下载完成后才会显示升级按钮
-   在会话页面和设置页面都会显示升级按钮
-   移动端完全不显示升级功能

## 工作流程

1. **应用启动** → 检测平台类型，只在桌面平台调用 `checkAndDownloadUpdate()`
2. **平台检测** → 如果是移动端，跳过更新流程
3. **检查更新** → 如果有更新，设置 `updateAvailable = true`
4. **静默下载** → 下载完成后设置 `updateDownloaded = true`
5. **显示按钮** → 只在桌面平台的 AppHeader 显示"升级"按钮
6. **用户点击** → 调用 `installUpdate()` 安装并重启

## 开发测试

在开发环境中，可以使用以下方法测试升级按钮的显示：

```javascript
// 在浏览器控制台中执行
import { simulateUpdateAvailable } from "@/utils/updater";
simulateUpdateAvailable();
```

这会模拟有更新可用的状态，升级按钮会立即显示在 AppHeader 中。

## 注意事项

-   **平台限制**：移动端（iOS/Android）不支持自更新功能，升级按钮不会显示
-   更新检查和下载都是静默进行的，不会打断用户的正常使用
-   只有在桌面平台下载完成后才会显示升级按钮
-   点击升级按钮后会立即重启应用，请确保用户数据已保存
-   升级过程中按钮会显示加载状态，防止重复点击
-   平台检测基于 UserAgent 和触控特性，准确识别移动设备

#
