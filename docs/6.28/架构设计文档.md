# Chat Assistant 架构设计文档

**文档版本**: v2.0  
**更新时间**: 2025-06-28  
**项目**: Chat Assistant (聊天助手)  
**技术栈**: Vue 3 + TypeScript + Tauri + Naive UI + Pinia

## 1. 项目概述

Chat Assistant 是一个基于 Tauri 的跨平台聊天助手应用，支持多种 AI 模型对话、思维链展示、消息管理等功能。项目正在从传统的 Service 层架构迁移到基于 DDD (领域驱动设计) 和 Clean Architecture (清洁架构) 的现代架构模式。

## 2. 当前架构状况分析

### 2.1 混合架构现状

项目目前处于架构迁移的过渡期，同时存在新旧两套架构：

#### 🔄 新架构 (DDD + Clean Architecture)
```
src/
├── domains/           # 领域层 - 业务实体和领域逻辑
├── usecases/          # 用例层 - 业务用例和应用逻辑  
├── infrastructure/    # 基础设施层 - 外部依赖实现
├── adapters/          # 适配器层 - 新旧架构桥接
└── core/              # 核心层 - 类型定义和工具
```

#### 🔧 遗留架构 (传统分层)
```
src/
├── services/          # 服务层 - 业务逻辑和数据访问
├── store/             # 状态管理 - Pinia stores
├── composables/       # 组合式函数 - Vue 3 composables
├── persistence/       # 持久化层 - 数据库操作
└── components/        # 组件层 - Vue 组件
```

### 2.2 当前问题分析

#### 🚨 主要问题
1. **架构混乱**: 新旧架构并存，依赖关系复杂
2. **职责不清**: Services 层承担过多职责
3. **紧耦合**: 组件直接依赖 Services，难以测试
4. **状态分散**: 业务状态在多个地方管理
5. **消息链路冗长**: 用户操作到 UI 更新路径过长

#### 📊 技术债务
- Services 层过重，单个文件职责过多
- Store 和 Service 之间存在循环依赖
- 缺乏统一的错误处理机制
- 测试覆盖率低，难以进行单元测试

## 3. 目标架构设计

### 3.1 架构原则

基于 **Clean Architecture** 和 **DDD** 的设计原则：

1. **依赖倒置**: 内层不依赖外层，通过接口解耦
2. **单一职责**: 每个层级和模块职责明确
3. **开闭原则**: 对扩展开放，对修改封闭
4. **领域驱动**: 以业务领域为核心组织代码

### 3.2 分层架构图

```mermaid
graph TB
    subgraph "UI Layer (表现层)"
        A[Vue Components]
        B[Views]
        C[Router]
    end
    
    subgraph "Adapter Layer (适配器层)"
        D[ChatStoreAdapter]
        E[Store Bridges]
    end
    
    subgraph "Use Case Layer (用例层)"
        F[SendMessageUseCase]
        G[ProcessThinkingUseCase]
        H[ChatManagementUseCases]
    end
    
    subgraph "Domain Layer (领域层)"
        I[Message Entity]
        J[Chat Entity]
        K[AI Provider Interface]
    end
    
    subgraph "Infrastructure Layer (基础设施层)"
        L[MessageRepository]
        M[ChatRepository]
        N[AIProvider]
        O[Database]
    end
    
    A --> D
    B --> D
    D --> F
    D --> G
    D --> H
    F --> I
    G --> I
    H --> J
    F --> K
    G --> K
    H --> K
    K --> N
    I --> L
    J --> M
    L --> O
    M --> O
```

### 3.3 核心层级详解

#### 🎯 Use Case Layer (用例层)
**职责**: 编排业务流程，协调领域对象完成业务用例

**主要用例**:
- `SendMessageUseCase`: 发送消息完整流程
- `ProcessThinkingUseCase`: 处理 AI 思维链
- `UpdateMessageUseCase`: 更新消息状态
- `CreateChatUseCase`: 创建新聊天
- `LoadChatUseCase`: 加载聊天历史

#### 🏗️ Domain Layer (领域层)  
**职责**: 核心业务逻辑和领域规则

**主要实体**:
- `Message`: 消息实体，包含内容、状态、角色等
- `Chat`: 聊天会话实体
- `ThinkingProcess`: 思维链处理逻辑
- `MessageStatus`: 消息状态枚举

#### 🔌 Infrastructure Layer (基础设施层)
**职责**: 外部依赖的具体实现

**主要组件**:
- `MessageRepositoryImpl`: 消息数据访问实现
- `ChatRepositoryImpl`: 聊天数据访问实现  
- `AIProviderImpl`: AI 服务提供者实现
- `DatabaseAdapter`: 数据库适配器

#### 🌉 Adapter Layer (适配器层)
**职责**: 新旧架构桥接，向后兼容

**主要适配器**:
- `ChatStoreAdapter`: 统一的业务操作接口
- Store 到 UseCase 的桥接适配器

## 4. 迁移策略

### 4.1 渐进式迁移路径

#### 阶段一: 建立新架构基础 ✅ (已完成)
- [x] 创建 DDD 目录结构
- [x] 实现核心用例层
- [x] 建立基础设施层接口和实现
- [x] 创建适配器层桥接新旧架构

#### 阶段二: 逐步替换遗留代码 🔄 (进行中)
- [ ] 重构 UI 组件使用适配器而非直接调用 Services
- [ ] 将 Store 逻辑迁移到用例层
- [ ] 简化 Services 层，仅保留数据访问逻辑
- [ ] 清理未使用的 Composables

#### 阶段三: 架构优化和清理 📋 (计划中)
- [ ] 移除冗余的 Services 层
- [ ] 优化依赖注入容器
- [ ] 完善错误处理机制
- [ ] 增加单元测试覆盖

#### 阶段四: 性能优化和监控 📋 (计划中)
- [ ] 实现用例级缓存
- [ ] 添加性能监控
- [ ] 优化消息流处理
- [ ] 完善日志系统

### 4.2 风险控制

1. **向后兼容**: 保持现有 API 接口不变
2. **渐进迁移**: 每次只迁移一个模块
3. **测试保障**: 每个阶段都有完整测试
4. **回滚机制**: 可随时回滚到稳定版本

## 5. 技术实现细节

### 5.1 依赖注入容器

使用单例模式的 `DIContainer` 管理所有依赖：

```typescript
export class DIContainer {
  private static instance: DIContainer;
  
  // 基础设施层实例
  private _messageRepository: IMessageRepository;
  private _chatRepository: IChatRepository;
  private _aiProvider: IAIProvider;
  
  // 用例实例
  private _sendMessageUseCase: SendMessageUseCase;
  // ... 其他用例
}
```

### 5.2 消息流处理

新架构下的消息处理流程：

```
用户输入 → ChatStoreAdapter → SendMessageUseCase → Domain Logic → Repository → Database
                ↓
UI Components ← Store Updates ← UseCase Events ← Domain Events
```

### 5.3 状态管理策略

- **Store**: 仅负责状态存储和响应式更新
- **UseCase**: 处理所有业务逻辑
- **Adapter**: 协调 Store 和 UseCase 之间的交互

## 6. 开发规范

### 6.1 代码组织规范

1. **用例命名**: 使用动词+名词格式，如 `SendMessageUseCase`
2. **接口定义**: 所有外部依赖都要定义接口
3. **错误处理**: 统一的错误处理和日志记录
4. **类型安全**: 严格的 TypeScript 类型检查

### 6.2 依赖关系规则

1. **内层不依赖外层**: Domain 层不能依赖 Infrastructure 层
2. **接口隔离**: 通过接口而非具体实现进行依赖
3. **单向依赖**: 避免循环依赖
4. **最小依赖**: 每个模块只依赖必要的接口

## 7. 监控和维护

### 7.1 架构健康度指标

- **依赖关系复杂度**: 监控模块间依赖关系
- **代码重复率**: 避免重复的业务逻辑
- **测试覆盖率**: 保持高质量的单元测试
- **性能指标**: 监控关键用例的执行时间

### 7.2 持续改进

1. **定期架构评审**: 每月评估架构健康度
2. **重构计划**: 持续优化代码质量
3. **技术债务管理**: 及时处理技术债务
4. **文档更新**: 保持架构文档的时效性

## 8. 当前实施状态

### 8.1 已完成的工作 ✅

#### DDD 架构基础建设
- **用例层**: 完整实现了所有核心业务用例
  - 消息相关: `SendMessageUseCase`, `ProcessThinkingUseCase`, `UpdateMessageUseCase`
  - 聊天相关: `CreateChatUseCase`, `LoadChatUseCase`, `RemoveChatUseCase`, `UpdateChatUseCase`, `LoadAllChatsUseCase`
- **基础设施层**: 完整的仓储模式实现
  - `MessageRepositoryImpl`, `ChatRepositoryImpl`, `AIProviderImpl`
  - 完全适配现有的 Services 和数据库层
- **适配器层**: `ChatStoreAdapter` 提供统一的业务接口
- **依赖注入**: `DIContainer` 管理所有依赖关系

#### 架构验证
- ✅ 构建成功，无类型错误
- ✅ 新架构与现有代码兼容
- ✅ 核心业务流程可正常运行

### 8.2 待完成的工作 📋

#### 高优先级 (立即执行)
1. **UI 层迁移**: 将所有 Vue 组件从直接调用 Services 改为使用 ChatStoreAdapter
2. **Store 重构**: 简化 useChatStore，移除业务逻辑，仅保留状态管理
3. **Services 清理**: 简化 aiService 和 summaryService，移除重复的业务逻辑

#### 中优先级 (后续优化)
1. **领域层完善**: 丰富 Message、Chat 等实体的业务方法
2. **测试覆盖**: 为用例层添加完整的单元测试
3. **性能优化**: 实现用例级缓存和批量更新机制

### 8.3 技术债务清单

#### 架构层面
- **双重依赖路径**: 组件可通过 Store 或 Adapter 访问业务逻辑
- **职责重叠**: Services 和 UseCases 存在功能重复
- **状态分散**: 消息状态在多个地方被管理

#### 代码层面
- **Services 过重**: `aiService.ts` 承担过多职责
- **循环依赖风险**: Store 和 Service 之间的相互引用
- **测试覆盖不足**: 缺乏用例层的单元测试

## 9. 立即行动计划

### 第一步: UI 层迁移 (本周)
**目标**: 所有组件使用 ChatStoreAdapter 而非直接调用 Services

**优先级排序**:
1. `EditArea` 组件 - 消息发送核心功能
2. `MsgList` 和 `MsgItem` - 消息显示和状态更新
3. `ChatList` - 聊天管理功能
4. `Setting` 页面 - 配置和数据导出

### 第二步: Store 简化 (下周)
**目标**: useChatStore 职责单一化

**重构重点**:
- 移除所有业务方法
- 保留状态定义和响应式更新
- 通过 Adapter 调用业务逻辑

### 第三步: Services 清理 (第三周)
**目标**: 简化 Services 层，避免职责重复

**清理策略**:
- aiService: 保留 API 调用，移除业务逻辑
- summaryService: 迁移到 SummaryUseCase
- 其他 Services: 保持现状作为 Repository 底层

---

**下一步行动**:
1. 🎯 **立即开始**: EditArea 组件迁移到 ChatStoreAdapter
2. 📋 **本周完成**: 所有 UI 组件使用新架构
3. 🔄 **持续监控**: 构建状态和功能完整性
4. 📊 **性能跟踪**: 确保迁移不影响用户体验
