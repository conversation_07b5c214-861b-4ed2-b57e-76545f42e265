# 架构迁移实施指南

**指南版本**: v1.0  
**创建时间**: 2025-06-28  
**适用范围**: Chat Assistant 项目架构迁移

## 1. 迁移总览

### 1.1 迁移目标

从传统的 Service 层架构迁移到基于 DDD 和 Clean Architecture 的现代架构，实现：
- 清晰的依赖关系和职责分离
- 更好的可测试性和可维护性
- 简化的消息处理流程
- 统一的业务逻辑管理

### 1.2 迁移原则

1. **渐进式迁移**: 保持系统持续可用
2. **向后兼容**: 不破坏现有功能
3. **测试驱动**: 每个步骤都有测试保障
4. **风险可控**: 可随时回滚到稳定状态

## 2. 迁移路线图

### 2.1 阶段划分

```mermaid
gantt
    title 架构迁移时间线
    dateFormat  YYYY-MM-DD
    section 阶段一
    UI层迁移           :a1, 2025-06-28, 7d
    Store重构          :a2, after a1, 7d
    section 阶段二  
    Services简化       :b1, after a2, 7d
    Composables迁移    :b2, after b1, 3d
    section 阶段三
    领域层完善         :c1, after b2, 7d
    性能优化           :c2, after c1, 5d
    section 阶段四
    测试完善           :d1, after c2, 5d
    文档更新           :d2, after d1, 3d
```

### 2.2 里程碑检查点

- **Week 1**: UI 层完全使用适配器
- **Week 2**: Store 层职责单一化
- **Week 3**: Services 层简化完成
- **Week 4**: 架构迁移全部完成

## 3. 详细实施步骤

### 3.1 阶段一: UI 层迁移 (Week 1)

#### Step 1.1: EditArea 组件迁移

**目标**: 消息输入组件使用新架构

**当前状态分析**:
```typescript
// 当前实现 (需要迁移)
import { useChatStore } from '@/store/useChatStore'
const chatStore = useChatStore()
await chatStore.sendMessage(content)
```

**迁移步骤**:

1. **引入适配器**:
```typescript
// 新实现
import { chatStoreAdapter } from '@/adapters/ChatStoreAdapter'
```

2. **替换发送逻辑**:
```typescript
// 旧方式
await chatStore.sendMessage(content)

// 新方式
await chatStoreAdapter.sendMessage(chatId, content, {
  onUserMessageCreated: (msg) => {
    // 处理用户消息创建
  },
  onAssistantMessageCreated: (msg) => {
    // 处理AI消息创建
  },
  onMessageUpdate: (msg) => {
    // 处理消息更新
  }
})
```

3. **测试验证**:
- 消息发送功能正常
- 流式响应正常显示
- 思维链处理正常
- 错误处理正常

#### Step 1.2: MsgList 和 MsgItem 组件迁移

**目标**: 消息显示组件使用新架构

**迁移重点**:
- 消息状态更新
- 重发功能
- 消息删除功能

**实施步骤**:

1. **消息更新逻辑**:
```typescript
// 使用适配器更新消息
await chatStoreAdapter.updateMessage(messageId, {
  content: newContent,
  status: MessageStatus.COMPLETED
})
```

2. **重发功能**:
```typescript
// 重发消息
await chatStoreAdapter.sendMessage(chatId, content, {
  resend: true,
  assistantMessageId: targetMessageId
})
```

#### Step 1.3: ChatList 组件迁移

**目标**: 聊天列表管理使用新架构

**迁移功能**:
- 创建新聊天
- 删除聊天
- 重命名聊天
- 加载聊天列表

**实施步骤**:

1. **聊天管理操作**:
```typescript
// 创建聊天
const chat = await chatStoreAdapter.createChat(title)

// 删除聊天  
await chatStoreAdapter.removeChat(chatId)

// 更新聊天
await chatStoreAdapter.updateChat(chatId, { short: newTitle })

// 加载所有聊天
const chats = await chatStoreAdapter.loadAllChats()
```

### 3.2 阶段二: Store 层重构 (Week 2)

#### Step 2.1: useChatStore 重构

**目标**: Store 仅负责状态管理，业务逻辑移到适配器

**重构策略**:

1. **保留状态定义**:
```typescript
export const useChatStore = defineStore('chat', () => {
  // 状态定义
  const chatList = ref<Chat[]>([])
  const activeChatId = ref<string>('')
  const messageList = ref<Msg[]>([])
  
  // 响应式计算
  const activeChat = computed(() => 
    chatList.value.find(chat => chat.id === activeChatId.value)
  )
  
  // 状态更新方法 (保留)
  const setChatList = (chats: Chat[]) => {
    chatList.value = chats
  }
  
  const setMessageList = (messages: Msg[]) => {
    messageList.value = messages
  }
  
  // 业务方法 (移除，使用适配器)
  // const sendMessage = async (...) => { ... } // 删除
  // const createChat = async (...) => { ... }  // 删除
})
```

2. **适配器集成**:
```typescript
// 在组件中使用
const chatStore = useChatStore()
const adapter = chatStoreAdapter

// 加载数据
const chats = await adapter.loadAllChats()
chatStore.setChatList(chats)

// 发送消息
await adapter.sendMessage(chatId, content, {
  onMessageUpdate: (msg) => {
    // 更新 Store 中的消息
    chatStore.updateMessage(msg)
  }
})
```

#### Step 2.2: 其他 Store 检查和优化

**检查清单**:
- `useEditAreaStore`: 主要是 UI 状态，保持现状
- `useEnvStore`: 配置管理，保持现状  
- `useGuideStore`: UI 状态，保持现状
- `useModelStore`: 配置管理，可能需要轻微调整

### 3.3 阶段三: Services 层简化 (Week 3)

#### Step 3.1: aiService 重构

**目标**: 拆分业务逻辑到用例层，保留纯 API 调用

**重构计划**:

1. **保留部分**:
```typescript
// aiService.ts - 保留纯 API 调用
export class AIService {
  async callAPI(messages: Message[], model: string): Promise<ReadableStream> {
    // 纯 API 调用逻辑
  }
  
  async getModels(): Promise<Model[]> {
    // 获取模型列表
  }
}
```

2. **迁移部分**:
- 流处理逻辑 → `SendMessageUseCase`
- 消息转换逻辑 → `ProcessThinkingUseCase`
- 状态管理逻辑 → 各个用例

#### Step 3.2: summaryService 迁移

**目标**: 创建 SummaryUseCase

**实施步骤**:

1. **创建用例**:
```typescript
// src/usecases/ai/SummaryUseCase.ts
export class SummaryUseCase extends UseCase<SummaryInput, SummaryOutput> {
  async execute(input: SummaryInput): Promise<SummaryOutput> {
    // 原 summaryService 的业务逻辑
  }
}
```

2. **更新 Composables**:
```typescript
// src/composables/useSummary.ts
export function useSummary() {
  const summaryUseCase = diContainer.summaryUseCase
  
  const generateSummary = async (content: string) => {
    return await summaryUseCase.execute({ content })
  }
  
  return { generateSummary }
}
```

### 3.4 阶段四: 完善和优化 (Week 4)

#### Step 4.1: 领域层完善

**目标**: 丰富领域模型和业务规则

**实施内容**:

1. **完善实体**:
```typescript
// src/domains/message/Message.ts
export class Message {
  constructor(
    public readonly id: string,
    public readonly chatId: string,
    public content: MessageContent,
    public readonly role: MessageRole,
    public status: MessageStatus
  ) {}
  
  // 业务方法
  public updateContent(content: MessageContent): void {
    this.content = content
  }
  
  public markAsCompleted(): void {
    this.status = MessageStatus.COMPLETED
  }
  
  // 业务规则
  public canBeEdited(): boolean {
    return this.role === MessageRole.USER && 
           this.status !== MessageStatus.SENDING
  }
}
```

2. **领域服务**:
```typescript
// src/domains/message/MessageDomainService.ts
export class MessageDomainService {
  public validateMessageContent(content: MessageContent): boolean {
    // 消息内容验证规则
  }
  
  public calculateMessageTokens(content: MessageContent): number {
    // Token 计算逻辑
  }
}
```

#### Step 4.2: 性能优化

**优化重点**:

1. **用例级缓存**:
```typescript
export class CachedSendMessageUseCase extends SendMessageUseCase {
  private cache = new Map<string, any>()
  
  async execute(input: SendMessageInput): Promise<SendMessageOutput> {
    const cacheKey = this.generateCacheKey(input)
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }
    
    const result = await super.execute(input)
    this.cache.set(cacheKey, result)
    return result
  }
}
```

2. **批量状态更新**:
```typescript
export class BatchMessageUpdater {
  private pendingUpdates: Map<string, Partial<Msg>> = new Map()
  
  public scheduleUpdate(messageId: string, updates: Partial<Msg>): void {
    this.pendingUpdates.set(messageId, {
      ...this.pendingUpdates.get(messageId),
      ...updates
    })
    
    this.debouncedFlush()
  }
  
  private debouncedFlush = debounce(() => {
    this.flushUpdates()
  }, 100)
}
```

## 4. 测试策略

### 4.1 测试层级

1. **单元测试**: 用例层和领域层
2. **集成测试**: 适配器层和基础设施层
3. **E2E 测试**: 完整用户流程

### 4.2 测试重点

**用例测试**:
```typescript
describe('SendMessageUseCase', () => {
  it('should create user message and AI response', async () => {
    // 测试消息发送完整流程
  })
  
  it('should handle thinking process correctly', async () => {
    // 测试思维链处理
  })
  
  it('should handle errors gracefully', async () => {
    // 测试错误处理
  })
})
```

**集成测试**:
```typescript
describe('ChatStoreAdapter', () => {
  it('should integrate with use cases correctly', async () => {
    // 测试适配器集成
  })
})
```

## 5. 风险管理

### 5.1 风险识别

1. **功能回归风险**: 迁移过程中功能异常
2. **性能下降风险**: 新架构影响性能
3. **开发效率风险**: 短期开发效率下降

### 5.2 风险缓解

1. **功能回归**:
   - 每个步骤都进行完整测试
   - 保持现有 API 兼容性
   - 准备快速回滚方案

2. **性能监控**:
   - 关键路径性能监控
   - 内存使用监控
   - 响应时间监控

3. **开发效率**:
   - 提供详细的迁移文档
   - 代码示例和最佳实践
   - 团队培训和知识分享

## 6. 成功标准

### 6.1 技术标准

- [ ] 所有 UI 组件使用适配器层
- [ ] Store 层职责单一化完成
- [ ] Services 层简化完成
- [ ] 单元测试覆盖率达到 80%
- [ ] 构建成功率 100%

### 6.2 质量标准

- [ ] 代码复杂度降低 30%
- [ ] 新功能开发效率提升 20%
- [ ] Bug 数量减少 50%
- [ ] 代码可维护性评分提升

### 6.3 用户体验标准

- [ ] 功能完全正常
- [ ] 性能无明显下降
- [ ] 用户操作流程无变化
- [ ] 错误处理更加友好

---

**开始迁移**: 从 EditArea 组件开始第一步迁移
