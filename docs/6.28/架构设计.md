# 聊天助手架构重构设计

**设计时间**: 2025-06-28
**设计目标**: 解决 Services 层过重、消息更新链路过长的问题
**核心理念**: 基于用例的清洁架构，明确依赖关系

## 当前架构问题分析

### 主要问题

1. **Services 层过重**
   - `aiService.ts` 承担了太多职责：API调用、消息转换、流处理
   - `messageService.ts` 既处理业务逻辑又处理数据持久化
   - Services 之间存在循环依赖和紧耦合

2. **消息更新链路过长**
   ```
   用户输入 → EditArea → ChatStore → AIService → MessageService → 数据库 → ChatStore → MsgItem
   ```
   这个链路涉及多个层级，状态传递复杂

3. **状态管理分散**
   - 消息状态在多个地方被修改
   - 思考状态的管理逻辑分散在多个组件中

## 新架构设计：基于用例的清洁架构

### 架构分层

```
┌─────────────────────────────────────────┐
│              UI Layer                   │
│  EditArea, MsgList, MsgItem, etc.      │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│            Use Cases Layer              │
│  SendMessageUseCase, ProcessThinking    │
│  UpdateMessageUseCase, etc.             │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│            Domain Layer                 │
│  Message, Chat, ThinkingProcess         │
│  MessageStatus, ContentProcessor        │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│         Infrastructure Layer            │
│  MessageRepository, AIProvider          │
│  DatabaseAdapter, etc.                  │
└─────────────────────────────────────────┘
```

### 核心用例设计

#### 1. SendMessageUseCase - 发送消息用例
```typescript
class SendMessageUseCase {
  constructor(
    private messageRepo: MessageRepository,
    private aiProvider: AIProvider,
    private chatRepo: ChatRepository
  ) {}

  async execute(input: SendMessageInput): Promise<SendMessageOutput> {
    // 1. 创建用户消息
    // 2. 创建AI占位消息
    // 3. 调用AI服务
    // 4. 处理流式响应
    // 5. 更新消息状态
  }
}
```

#### 2. ProcessThinkingUseCase - 处理思考过程用例
```typescript
class ProcessThinkingUseCase {
  async execute(content: string, messageId: string): Promise<ThinkingResult> {
    // 1. 解析思考内容
    // 2. 判断思考状态
    // 3. 更新消息状态
    // 4. 返回处理结果
  }
}
```

#### 3. HandleStreamUseCase - 处理流式响应用例
```typescript
class HandleStreamUseCase {
  constructor(
    private processThinking: ProcessThinkingUseCase,
    private updateMessage: UpdateMessageUseCase
  ) {}

  async execute(stream: AsyncIterable<string>, messageId: string): Promise<void> {
    // 1. 监听流式数据
    // 2. 实时处理内容
    // 3. 管理状态转换
    // 4. 更新UI状态
  }
}
```

### 依赖关系图

```
UI Components
    ↓ (调用)
Use Cases
    ↓ (使用)
Domain Models
    ↓ (通过)
Repositories/Providers
    ↓ (访问)
Database/API
```

**依赖规则**:
- 内层不依赖外层
- 外层可以依赖内层
- 同层之间通过接口通信

## 重构实施计划

### 第一阶段：建立用例层 (Week 1)

1. **创建用例基础设施**
   ```
   src/usecases/
   ├── base/
   │   ├── UseCase.ts           # 用例基类
   │   └── UseCaseExecutor.ts   # 用例执行器
   ├── message/
   │   ├── SendMessageUseCase.ts
   │   ├── UpdateMessageUseCase.ts
   │   └── ProcessThinkingUseCase.ts
   ├── chat/
   │   ├── CreateChatUseCase.ts
   │   └── LoadChatUseCase.ts
   └── ai/
       ├── HandleStreamUseCase.ts
       └── CallAIUseCase.ts
   ```

2. **重构现有 Services**
   - 将 `aiService.ts` 拆分为多个用例
   - 将 `messageService.ts` 的业务逻辑提取到用例中
   - 保留纯数据访问逻辑作为 Repository

### 第二阶段：优化领域模型 (Week 2)

1. **创建领域模型**
   ```
   src/domains/
   ├── message/
   │   ├── Message.ts           # 消息实体
   │   ├── MessageStatus.ts     # 状态枚举
   │   └── ContentProcessor.ts  # 内容处理器
   ├── chat/
   │   └── Chat.ts              # 聊天实体
   └── ai/
       ├── ThinkingProcess.ts   # 思考过程
       └── StreamContent.ts     # 流式内容
   ```

2. **重构状态管理**
   - Store 只负责状态存储和响应式更新
   - 业务逻辑全部移到用例中
   - 简化组件与 Store 的交互

### 第三阶段：简化消息链路 (Week 3)

1. **新的消息流程**
   ```
   用户输入 → SendMessageUseCase → Domain Models → Repository → Database
                ↓
   UI Components ← Store ← UseCase Events
   ```

2. **状态同步机制**
   - 用例执行完成后更新 Store
   - Store 变化自动触发 UI 更新
   - 减少中间状态传递

### 第四阶段：性能优化和测试 (Week 4)

1. **性能优化**
   - 用例级别的缓存
   - 批量状态更新
   - 组件渲染优化

2. **测试覆盖**
   - 用例单元测试
   - 集成测试
   - E2E 测试

## 关键优势

### 1. 清晰的依赖关系
- 单向依赖，避免循环引用
- 每个用例职责单一，易于理解
- 便于单元测试和维护

### 2. 简化的消息链路
- 减少中间层级
- 状态变化路径清晰
- 更好的性能表现

### 3. 更好的可扩展性
- 新功能只需添加新用例
- 不影响现有代码结构
- 便于功能模块化

### 4. 易于调试和维护
- 每个用例都是独立的业务单元
- 错误定位更加精确
- 代码逻辑更加清晰

## 迁移策略

### 渐进式重构
1. 保持现有代码正常运行
2. 逐步引入用例层
3. 逐个替换现有 Services
4. 最后清理废弃代码

### 风险控制
1. 每个阶段都有完整的测试
2. 可以随时回滚到上一个稳定版本
3. 功能验证优先于架构完美

## 用例设计详细说明

### SendMessageUseCase 详细设计

```typescript
interface SendMessageInput {
  chatId: string;
  content: MessageContent;
  resend?: boolean;
  assistantMessageId?: string;
}

interface SendMessageOutput {
  userMessage?: Msg;
  assistantMessage: Msg;
  success: boolean;
}

class SendMessageUseCase {
  constructor(
    private messageRepo: MessageRepository,
    private aiProvider: AIProvider,
    private chatRepo: ChatRepository,
    private processThinking: ProcessThinkingUseCase,
    private updateMessage: UpdateMessageUseCase
  ) {}

  async execute(input: SendMessageInput): Promise<SendMessageOutput> {
    // 1. 验证输入
    this.validateInput(input);

    // 2. 创建或获取聊天
    const chat = await this.ensureChatExists(input.chatId);

    // 3. 创建用户消息（如果不是重发）
    let userMessage: Msg | undefined;
    if (!input.resend) {
      userMessage = await this.messageRepo.create({
        chatId: chat.id,
        content: input.content,
        role: 'user'
      });
    }

    // 4. 创建或更新AI消息
    const assistantMessage = await this.createOrUpdateAssistantMessage(input);

    // 5. 调用AI并处理流式响应
    await this.handleAIResponse(chat.id, assistantMessage.id, input);

    return {
      userMessage,
      assistantMessage,
      success: true
    };
  }

  private async handleAIResponse(chatId: string, messageId: string, input: SendMessageInput) {
    const stream = await this.aiProvider.sendMessage({
      chatId,
      content: input.content,
      maxMessages: 10
    });

    let fullContent = '';
    for await (const chunk of stream) {
      fullContent += chunk;

      // 处理思考过程
      const thinkingResult = await this.processThinking.execute(fullContent, messageId);

      // 更新消息
      await this.updateMessage.execute({
        messageId,
        content: thinkingResult.mainContent,
        thinkContent: thinkingResult.thinkContent,
        status: thinkingResult.status
      });
    }
  }
}
```

### 消息状态流转

```typescript
enum MessageStatus {
  WAITING = 'waiting',
  THINKING = 'thinking',
  GENERATING = 'generating',
  COMPLETED = 'completed',
  ERROR = 'error'
}

// 状态转换规则
const STATUS_TRANSITIONS = {
  [MessageStatus.WAITING]: [MessageStatus.THINKING, MessageStatus.GENERATING],
  [MessageStatus.THINKING]: [MessageStatus.GENERATING, MessageStatus.ERROR],
  [MessageStatus.GENERATING]: [MessageStatus.COMPLETED, MessageStatus.ERROR],
  [MessageStatus.COMPLETED]: [],
  [MessageStatus.ERROR]: [MessageStatus.WAITING] // 可以重试
};
```

## 下一步行动

1. **确认架构方案** - 讨论并确定最终方案
2. **创建第一个用例** - 从 SendMessageUseCase 开始
3. **建立测试框架** - 确保重构质量
4. **制定详细时间表** - 分阶段实施计划

---

**注意**: 这个架构设计基于清洁架构原则，但针对前端单人开发场景进行了简化，避免过度设计的同时保持代码的清晰性和可维护性。用例模式比事件总线更适合你的需求，因为它提供了更清晰的依赖关系和更容易理解的数据流向。