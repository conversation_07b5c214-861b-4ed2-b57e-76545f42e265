{"$schema": "https://schema.tauri.app/config/2", "productName": "PigAI", "version": "0.34.0", "identifier": "com.guohere.chatpig", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"withGlobalTauri": true, "windows": [{"title": "Pig AI", "hiddenTitle": true, "width": 800, "height": 600, "titleBarStyle": "Overlay"}], "security": {"csp": null}, "macOSPrivateApi": false}, "bundle": {"createUpdaterArtifacts": true, "active": true, "category": "Productivity", "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "windows": {}, "macOS": {"signingIdentity": "Developer ID Application: guo sun (U745R97LKR)", "providerShortName": "U745R97LKR"}, "publisher": "CN=C90664AE-B85C-4AE4-8F51-B90D9722BB5B"}, "plugins": {"globalShortcut": {"shortcuts": ["CommandOrControl+K"]}, "updater": {"pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDlGNjcyOTkxMjRDREFBNEIKUldSTHFzMGtrU2xubjgyM2RhMTlvMENWMGUzbG9jaU9ZckRtZDkyNFRTT3BoSFNNRkwrN1RNN3MK", "endpoints": ["https://dist.guohere.com/latest.json"]}}}